<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_gravity="bottom"
    android:orientation="vertical"
    android:background="#cc000000"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layoutDirection="ltr">
    <LinearLayout
        android:gravity="center"
        android:orientation="horizontal"
        android:paddingTop="4dp"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">
        <ImageButton
            android:id="@+id/exo_prev"
            style="@style/ExoMediaButton.Previous"/>
        <ImageButton
            android:id="@+id/exo_rew"
            style="@style/ExoMediaButton.Rewind"/>
        <ImageButton
            android:id="@+id/exo_shuffle"
            style="@style/ExoMediaButton"/>
        <ImageButton
            android:id="@+id/exo_repeat_toggle"
            style="@style/ExoMediaButton"/>
        <ImageButton
            android:id="@+id/exo_play"
            style="@style/ExoMediaButton.Play"/>
        <ImageButton
            android:id="@+id/exo_pause"
            style="@style/ExoMediaButton.Pause"/>
        <ImageButton
            android:id="@+id/exo_ffwd"
            style="@style/ExoMediaButton.FastForward"/>
        <ImageButton
            android:id="@+id/exo_next"
            style="@style/ExoMediaButton.Next"/>
        <ImageButton
            android:id="@+id/exo_vr"
            style="@style/ExoMediaButton.VR"/>
    </LinearLayout>
    <LinearLayout
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="4dp">
        <TextView
            android:textSize="14sp"
            android:textStyle="bold"
            android:textColor="#ffbebebe"
            android:id="@+id/exo_position"
            android:paddingLeft="4dp"
            android:paddingRight="4dp"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:includeFontPadding="false"/>
        <View
            android:id="@+id/exo_progress_placeholder"
            android:layout_width="0dp"
            android:layout_height="26dp"
            android:layout_weight="1"/>
        <TextView
            android:textSize="14sp"
            android:textStyle="bold"
            android:textColor="#ffbebebe"
            android:id="@+id/exo_duration"
            android:paddingLeft="4dp"
            android:paddingRight="4dp"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:includeFontPadding="false"/>
    </LinearLayout>
</LinearLayout>
