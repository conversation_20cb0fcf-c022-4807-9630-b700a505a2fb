<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@color/background"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:backgroundTint="@color/background">
    <com.google.android.material.button.MaterialButton
        android:id="@+id/backBtn"
        android:background="?attr/selectableItemBackgroundBorderless"
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:contentDescription="@string/back_button"
        android:layout_marginStart="12dp"
        android:layout_marginEnd="6dp"
        app:icon="@drawable/ic_back"
        app:iconGravity="textStart"
        app:iconPadding="-4dp"
        app:iconSize="20dp"
        app:iconTint="@color/textColor"
        app:layout_constraintBottom_toBottomOf="@+id/headerText"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/headerText"
        style="@style/Widget.MaterialComponents.Button.Icon"/>
    <TextView
        android:textSize="32sp"
        android:textStyle="bold"
        android:textColor="@color/textColor"
        android:id="@+id/headerText"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="44dp"
        android:text="@string/notifications"
        android:layout_marginStart="12dp"
        app:layout_constraintStart_toEndOf="@+id/backBtn"
        app:layout_constraintTop_toTopOf="parent"/>
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recyclerViewNotifications"
        android:paddingLeft="8dp"
        android:paddingRight="8dp"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginTop="16dp"
        android:paddingHorizontal="8dp"
        app:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
        app:layout_behavior="@string/appbar_scrolling_view_behavior"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/headerText"
        app:spanCount="1"/>
    <TextView
        android:textColor="#bcbcbc"
        android:id="@+id/emptyListText"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/empty_inbox"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>
</androidx.constraintlayout.widget.ConstraintLayout>
