package com.enhanced.videowidget.ui

import android.Manifest
import android.app.Activity
import android.content.Intent
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Bundle
import android.provider.MediaStore
import android.util.Log
import android.widget.Toast
import androidx.activity.result.contract.ActivityResultContracts
import androidx.appcompat.app.AppCompatActivity
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import com.enhanced.videowidget.R
import com.enhanced.videowidget.data.model.Widget
import com.enhanced.videowidget.data.repository.WidgetRepository
// import com.enhanced.videowidget.databinding.ActivityMainBinding
import com.enhanced.videowidget.ui.adapter.WidgetListAdapter
import com.enhanced.videowidget.utils.VideoFrameExtractor
import kotlinx.coroutines.launch

/**
 * Main activity for managing video widgets
 */
class MainActivity : AppCompatActivity() {
    
    companion object {
        private const val TAG = "MainActivity"
        private const val PERMISSION_REQUEST_CODE = 1001
    }
    
    // private lateinit var binding: ActivityMainBinding
    private lateinit var repository: WidgetRepository
    private lateinit var widgetAdapter: WidgetListAdapter
    
    private val videoPickerLauncher = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) { result ->
        if (result.resultCode == Activity.RESULT_OK) {
            result.data?.data?.let { uri ->
                handleVideoSelection(uri)
            }
        }
    }
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_main)

        setupRepository()
        setupUI()
        checkPermissions()
    }
    
    private fun setupRepository() {
        repository = WidgetRepository.getInstance()
        repository.initialize(this)
    }
    
    private fun setupUI() {
        val toolbar = findViewById<androidx.appcompat.widget.Toolbar>(R.id.toolbar)
        setSupportActionBar(toolbar)
        supportActionBar?.title = getString(R.string.main_activity_title)

        // Setup RecyclerView
        widgetAdapter = WidgetListAdapter(
            onWidgetClick = { widget -> handleWidgetClick(widget) },
            onDeleteClick = { widget -> handleDeleteWidget(widget) }
        )

        val recyclerView = findViewById<androidx.recyclerview.widget.RecyclerView>(R.id.recyclerViewWidgets)
        recyclerView.apply {
            layoutManager = androidx.recyclerview.widget.LinearLayoutManager(this@MainActivity)
            adapter = widgetAdapter
        }

        // Setup FAB
        val fabAddWidget = findViewById<com.google.android.material.floatingactionbutton.FloatingActionButton>(R.id.fabAddWidget)
        fabAddWidget.setOnClickListener {
            if (hasStoragePermission()) {
                openVideoPicker()
            } else {
                requestStoragePermission()
            }
        }

        // Observe widgets
        repository.widgets.observe(this) { widgets ->
            updateWidgetList(widgets)
        }
    }
    
    private fun updateWidgetList(widgets: List<Widget>) {
        widgetAdapter.submitList(widgets)

        val textViewNoWidgets = findViewById<android.widget.LinearLayout>(R.id.textViewNoWidgets)
        val recyclerViewWidgets = findViewById<androidx.recyclerview.widget.RecyclerView>(R.id.recyclerViewWidgets)

        if (widgets.isEmpty()) {
            textViewNoWidgets.visibility = android.view.View.VISIBLE
            recyclerViewWidgets.visibility = android.view.View.GONE
        } else {
            textViewNoWidgets.visibility = android.view.View.GONE
            recyclerViewWidgets.visibility = android.view.View.VISIBLE
        }
    }
    
    private fun handleWidgetClick(widget: Widget) {
        // Toggle play/pause state
        repository.toggleWidgetPlayPause(widget.id)
        Toast.makeText(
            this,
            if (widget.isPaused) getString(R.string.action_play) else getString(R.string.action_pause),
            Toast.LENGTH_SHORT
        ).show()
    }
    
    private fun handleDeleteWidget(widget: Widget) {
        repository.removeWidget(widget.id)
        Toast.makeText(this, getString(R.string.widget_deleted), Toast.LENGTH_SHORT).show()
    }
    
    private fun openVideoPicker() {
        val intent = Intent(Intent.ACTION_PICK, MediaStore.Video.Media.EXTERNAL_CONTENT_URI).apply {
            type = "video/*"
        }
        videoPickerLauncher.launch(intent)
    }
    
    private fun handleVideoSelection(videoUri: Uri) {
        Log.d(TAG, "Video selected: $videoUri")
        
        lifecycleScope.launch {
            try {
                // Show loading
                val progressBar = findViewById<android.widget.ProgressBar>(R.id.progressBar)
                val fabAddWidget = findViewById<com.google.android.material.floatingactionbutton.FloatingActionButton>(R.id.fabAddWidget)
                progressBar.visibility = android.view.View.VISIBLE
                fabAddWidget.isEnabled = false
                
                // Get video path
                val videoPath = getVideoPath(videoUri)
                if (videoPath == null) {
                    showError(getString(R.string.error_file_not_found))
                    return@launch
                }
                
                // Extract video metadata
                val frameExtractor = VideoFrameExtractor()
                val metadata = frameExtractor.getVideoMetadata(videoPath)
                
                if (metadata == null) {
                    showError(getString(R.string.error_video_processing))
                    return@launch
                }
                
                // Create new widget
                val widgetId = generateWidgetId()
                val widget = Widget(
                    id = widgetId,
                    name = "Video Widget $widgetId",
                    description = "Duration: ${metadata.duration / 1000}s",
                    videoPath = videoPath,
                    framerate = metadata.frameRate,
                    mediaType = "video"
                )
                
                // Add widget to repository
                repository.addOrUpdateWidget(widget)
                
                Toast.makeText(this@MainActivity, getString(R.string.widget_created), Toast.LENGTH_SHORT).show()
                
            } catch (e: Exception) {
                Log.e(TAG, "Error processing video", e)
                showError(getString(R.string.error_widget_creation))
            } finally {
                // Hide loading
                val progressBar = findViewById<android.widget.ProgressBar>(R.id.progressBar)
                val fabAddWidget = findViewById<com.google.android.material.floatingactionbutton.FloatingActionButton>(R.id.fabAddWidget)
                progressBar.visibility = android.view.View.GONE
                fabAddWidget.isEnabled = true
            }
        }
    }
    
    private fun getVideoPath(uri: Uri): String? {
        val projection = arrayOf(MediaStore.Video.Media.DATA)
        contentResolver.query(uri, projection, null, null, null)?.use { cursor ->
            if (cursor.moveToFirst()) {
                val columnIndex = cursor.getColumnIndexOrThrow(MediaStore.Video.Media.DATA)
                return cursor.getString(columnIndex)
            }
        }
        return null
    }
    
    private fun generateWidgetId(): Int {
        return (System.currentTimeMillis() % Int.MAX_VALUE).toInt()
    }
    
    private fun showError(message: String) {
        Toast.makeText(this, message, Toast.LENGTH_LONG).show()
    }
    
    private fun hasStoragePermission(): Boolean {
        return ContextCompat.checkSelfPermission(
            this,
            Manifest.permission.READ_EXTERNAL_STORAGE
        ) == PackageManager.PERMISSION_GRANTED
    }
    
    private fun requestStoragePermission() {
        ActivityCompat.requestPermissions(
            this,
            arrayOf(Manifest.permission.READ_EXTERNAL_STORAGE),
            PERMISSION_REQUEST_CODE
        )
    }
    
    private fun checkPermissions() {
        if (!hasStoragePermission()) {
            requestStoragePermission()
        }
    }
    
    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<out String>,
        grantResults: IntArray
    ) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        
        when (requestCode) {
            PERMISSION_REQUEST_CODE -> {
                if (grantResults.isNotEmpty() && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                    Toast.makeText(this, getString(R.string.permission_granted), Toast.LENGTH_SHORT).show()
                } else {
                    Toast.makeText(this, getString(R.string.permission_denied), Toast.LENGTH_SHORT).show()
                }
            }
        }
    }
}
