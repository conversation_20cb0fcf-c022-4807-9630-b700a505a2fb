package com.enhanced.videowidget.ui;

import java.lang.System;

/**
 * Main activity for managing video widgets
 */
@kotlin.Metadata(mv = {1, 8, 0}, k = 1, d1 = {"\u0000l\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u0011\n\u0000\n\u0002\u0010\u0015\n\u0002\b\t\n\u0002\u0010 \n\u0002\b\u0002\u0018\u0000 .2\u00020\u0001:\u0001.B\u0005\u00a2\u0006\u0002\u0010\u0002J\b\u0010\u000b\u001a\u00020\fH\u0002J\b\u0010\r\u001a\u00020\u000eH\u0002J\u0012\u0010\u000f\u001a\u0004\u0018\u00010\u00102\u0006\u0010\u0011\u001a\u00020\u0012H\u0002J\u0010\u0010\u0013\u001a\u00020\f2\u0006\u0010\u0014\u001a\u00020\u0015H\u0002J\u0010\u0010\u0016\u001a\u00020\f2\u0006\u0010\u0017\u001a\u00020\u0012H\u0002J\u0010\u0010\u0018\u001a\u00020\f2\u0006\u0010\u0014\u001a\u00020\u0015H\u0002J\b\u0010\u0019\u001a\u00020\u001aH\u0002J\u0012\u0010\u001b\u001a\u00020\f2\b\u0010\u001c\u001a\u0004\u0018\u00010\u001dH\u0014J-\u0010\u001e\u001a\u00020\f2\u0006\u0010\u001f\u001a\u00020\u000e2\u000e\u0010 \u001a\n\u0012\u0006\b\u0001\u0012\u00020\u00100!2\u0006\u0010\"\u001a\u00020#H\u0016\u00a2\u0006\u0002\u0010$J\b\u0010%\u001a\u00020\fH\u0002J\b\u0010&\u001a\u00020\fH\u0002J\b\u0010\'\u001a\u00020\fH\u0002J\b\u0010(\u001a\u00020\fH\u0002J\u0010\u0010)\u001a\u00020\f2\u0006\u0010*\u001a\u00020\u0010H\u0002J\u0016\u0010+\u001a\u00020\f2\f\u0010,\u001a\b\u0012\u0004\u0012\u00020\u00150-H\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082.\u00a2\u0006\u0002\n\u0000R\u001c\u0010\u0005\u001a\u0010\u0012\f\u0012\n \b*\u0004\u0018\u00010\u00070\u00070\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\nX\u0082.\u00a2\u0006\u0002\n\u0000\u00a8\u0006/"}, d2 = {"Lcom/enhanced/videowidget/ui/MainActivity;", "Landroidx/appcompat/app/AppCompatActivity;", "()V", "repository", "Lcom/enhanced/videowidget/data/repository/WidgetRepository;", "videoPickerLauncher", "Landroidx/activity/result/ActivityResultLauncher;", "Landroid/content/Intent;", "kotlin.jvm.PlatformType", "widgetAdapter", "Lcom/enhanced/videowidget/ui/adapter/WidgetListAdapter;", "checkPermissions", "", "generateWidgetId", "", "getVideoPath", "", "uri", "Landroid/net/Uri;", "handleDeleteWidget", "widget", "Lcom/enhanced/videowidget/data/model/Widget;", "handleVideoSelection", "videoUri", "handleWidgetClick", "hasStoragePermission", "", "onCreate", "savedInstanceState", "Landroid/os/Bundle;", "onRequestPermissionsResult", "requestCode", "permissions", "", "grantResults", "", "(I[Ljava/lang/String;[I)V", "openVideoPicker", "requestStoragePermission", "setupRepository", "setupUI", "showError", "message", "updateWidgetList", "widgets", "", "Companion", "app_debug"})
public final class MainActivity extends androidx.appcompat.app.AppCompatActivity {
    @org.jetbrains.annotations.NotNull
    public static final com.enhanced.videowidget.ui.MainActivity.Companion Companion = null;
    private static final java.lang.String TAG = "MainActivity";
    private static final int PERMISSION_REQUEST_CODE = 1001;
    private com.enhanced.videowidget.data.repository.WidgetRepository repository;
    private com.enhanced.videowidget.ui.adapter.WidgetListAdapter widgetAdapter;
    private final androidx.activity.result.ActivityResultLauncher<android.content.Intent> videoPickerLauncher = null;
    
    public MainActivity() {
        super();
    }
    
    @java.lang.Override
    protected void onCreate(@org.jetbrains.annotations.Nullable
    android.os.Bundle savedInstanceState) {
    }
    
    private final void setupRepository() {
    }
    
    private final void setupUI() {
    }
    
    private final void updateWidgetList(java.util.List<com.enhanced.videowidget.data.model.Widget> widgets) {
    }
    
    private final void handleWidgetClick(com.enhanced.videowidget.data.model.Widget widget) {
    }
    
    private final void handleDeleteWidget(com.enhanced.videowidget.data.model.Widget widget) {
    }
    
    private final void openVideoPicker() {
    }
    
    private final void handleVideoSelection(android.net.Uri videoUri) {
    }
    
    private final java.lang.String getVideoPath(android.net.Uri uri) {
        return null;
    }
    
    private final int generateWidgetId() {
        return 0;
    }
    
    private final void showError(java.lang.String message) {
    }
    
    private final boolean hasStoragePermission() {
        return false;
    }
    
    private final void requestStoragePermission() {
    }
    
    private final void checkPermissions() {
    }
    
    @java.lang.Override
    public void onRequestPermissionsResult(int requestCode, @org.jetbrains.annotations.NotNull
    java.lang.String[] permissions, @org.jetbrains.annotations.NotNull
    int[] grantResults) {
    }
    
    @kotlin.Metadata(mv = {1, 8, 0}, k = 1, d1 = {"\u0000\u0018\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0000\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0006X\u0082T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0007"}, d2 = {"Lcom/enhanced/videowidget/ui/MainActivity$Companion;", "", "()V", "PERMISSION_REQUEST_CODE", "", "TAG", "", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
    }
}