package com.enhanced.videowidget.ui

import android.Manifest
import android.app.Activity
import android.appwidget.AppWidgetManager
import android.content.Intent
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.provider.MediaStore
import android.util.Log
import android.widget.Toast
import androidx.activity.result.contract.ActivityResultContracts
import androidx.appcompat.app.AppCompatActivity
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import androidx.lifecycle.lifecycleScope
import com.enhanced.videowidget.utils.AppLogger
import com.enhanced.videowidget.R
import com.enhanced.videowidget.data.model.Widget
import com.enhanced.videowidget.data.repository.WidgetRepository
// import com.enhanced.videowidget.databinding.ActivityWidgetConfigurationBinding
import com.enhanced.videowidget.utils.VideoFrameExtractor
import com.enhanced.videowidget.widget.EnhancedVideoWidget
import kotlinx.coroutines.launch

/**
 * Configuration activity for setting up new video widgets
 */
class WidgetConfigurationActivity : AppCompatActivity() {
    
    companion object {
        private const val TAG = "WidgetConfiguration"
    }
    
    // private lateinit var binding: ActivityWidgetConfigurationBinding
    private lateinit var repository: WidgetRepository
    private var appWidgetId = AppWidgetManager.INVALID_APPWIDGET_ID
    private var selectedVideoUri: Uri? = null
    private var selectedVideoPath: String? = null
    
    private val videoPickerLauncher = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) { result ->
        AppLogger.d(AppLogger.TAG_CONFIG, "Video picker result: ${result.resultCode}")
        if (result.resultCode == Activity.RESULT_OK) {
            result.data?.data?.let { uri ->
                AppLogger.logVideoSelection(null, uri.toString())
                handleVideoSelection(uri)
            } ?: run {
                AppLogger.w(AppLogger.TAG_CONFIG, "Video picker returned OK but no URI data")
            }
        } else {
            AppLogger.w(AppLogger.TAG_CONFIG, "Video picker cancelled or failed")
        }
    }

    private val permissionLauncher = registerForActivityResult(
        ActivityResultContracts.RequestMultiplePermissions()
    ) { permissions ->
        val allGranted = permissions.values.all { it }
        if (allGranted) {
            openVideoPicker()
        } else {
            // Check if user denied permanently
            val deniedPermissions = permissions.filterValues { !it }.keys
            val shouldShowRationale = deniedPermissions.any { permission ->
                ActivityCompat.shouldShowRequestPermissionRationale(this, permission)
            }

            if (shouldShowRationale) {
                showError(getString(R.string.permission_video_required))
            } else {
                showError(getString(R.string.permission_settings_required))
            }
        }
    }
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_widget_configuration)

        AppLogger.i(AppLogger.TAG_CONFIG, "=== WIDGET CONFIGURATION ACTIVITY STARTED ===")
        AppLogger.d(AppLogger.TAG_CONFIG, "Debug logging enabled: ${AppLogger.isDebugEnabled()}")
        AppLogger.d(AppLogger.TAG_CONFIG, "Verbose logging enabled: ${AppLogger.isVerboseEnabled()}")
        
        // Set the result to CANCELED initially
        setResult(RESULT_CANCELED)
        
        // Get the widget ID from the intent
        appWidgetId = intent?.extras?.getInt(
            AppWidgetManager.EXTRA_APPWIDGET_ID,
            AppWidgetManager.INVALID_APPWIDGET_ID
        ) ?: AppWidgetManager.INVALID_APPWIDGET_ID
        
        if (appWidgetId == AppWidgetManager.INVALID_APPWIDGET_ID) {
            finish()
            return
        }
        
        setupRepository()
        setupUI()
    }
    
    private fun setupRepository() {
        repository = WidgetRepository.getInstance()
        repository.initialize(this)
    }
    
    private fun setupUI() {
        // Set up toolbar
        val toolbar = findViewById<androidx.appcompat.widget.Toolbar>(R.id.toolbar)
        setSupportActionBar(toolbar)
        supportActionBar?.apply {
            title = getString(R.string.widget_configuration_title)
            setDisplayHomeAsUpEnabled(true)
        }

        // Set up video selection button
        val buttonSelectVideo = findViewById<com.google.android.material.button.MaterialButton>(R.id.buttonSelectVideo)
        buttonSelectVideo.setOnClickListener {
            checkPermissionsAndOpenPicker()
        }

        // Set up create widget button
        val buttonCreateWidget = findViewById<com.google.android.material.button.MaterialButton>(R.id.buttonCreateWidget)
        buttonCreateWidget.setOnClickListener {
            createWidget()
        }

        // Set up cancel button
        val buttonCancel = findViewById<com.google.android.material.button.MaterialButton>(R.id.buttonCancel)
        buttonCancel.setOnClickListener {
            finish()
        }

        // Initially disable create button
        buttonCreateWidget.isEnabled = false
    }

    private fun checkPermissionsAndOpenPicker() {
        val permissions = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            arrayOf(Manifest.permission.READ_MEDIA_VIDEO)
        } else {
            arrayOf(Manifest.permission.READ_EXTERNAL_STORAGE)
        }

        val allPermissionsGranted = permissions.all { permission ->
            ContextCompat.checkSelfPermission(this, permission) == PackageManager.PERMISSION_GRANTED
        }

        if (allPermissionsGranted) {
            openVideoPicker()
        } else {
            permissionLauncher.launch(permissions)
        }
    }

    private fun openVideoPicker() {
        try {
            val intent = Intent(Intent.ACTION_PICK, MediaStore.Video.Media.EXTERNAL_CONTENT_URI).apply {
                type = "video/*"
                addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
            }

            // Check if there's an app that can handle this intent
            if (intent.resolveActivity(packageManager) != null) {
                videoPickerLauncher.launch(intent)
            } else {
                // Fallback to generic file picker
                val fallbackIntent = Intent(Intent.ACTION_GET_CONTENT).apply {
                    type = "video/*"
                    addCategory(Intent.CATEGORY_OPENABLE)
                    addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
                }
                videoPickerLauncher.launch(fallbackIntent)
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error opening video picker", e)
            showError(getString(R.string.video_picker_error))
        }
    }
    
    private fun handleVideoSelection(videoUri: Uri) {
        AppLogger.i(AppLogger.TAG_CONFIG, "=== HANDLING VIDEO SELECTION ===")
        AppLogger.d(AppLogger.TAG_CONFIG, "Selected video URI: $videoUri")

        selectedVideoUri = videoUri
        selectedVideoPath = getVideoPath(videoUri)

        AppLogger.d(AppLogger.TAG_CONFIG, "Video path resolved to: $selectedVideoPath")
        
        if (selectedVideoPath != null) {
            val textViewSelectedVideo = findViewById<android.widget.TextView>(R.id.textViewSelectedVideo)
            val buttonCreateWidget = findViewById<com.google.android.material.button.MaterialButton>(R.id.buttonCreateWidget)
            textViewSelectedVideo.text = getString(R.string.video_selected)
            buttonCreateWidget.isEnabled = true
            
            // Show video preview if possible
            lifecycleScope.launch {
                try {
                    val frameExtractor = VideoFrameExtractor()
                    val metadata = frameExtractor.getVideoMetadata(selectedVideoPath!!)
                    
                    metadata?.let {
                        val previewFrame = frameExtractor.extractFrameAtTime(selectedVideoPath!!, 0)
                        previewFrame?.let { bitmap ->
                            val imageViewPreview = findViewById<android.widget.ImageView>(R.id.imageViewPreview)
                            imageViewPreview.setImageBitmap(bitmap)
                            imageViewPreview.visibility = android.view.View.VISIBLE
                        }

                        val textViewVideoInfo = findViewById<android.widget.TextView>(R.id.textViewVideoInfo)
                        textViewVideoInfo.text = "Duration: ${it.duration / 1000}s, ${it.width}x${it.height}"
                        textViewVideoInfo.visibility = android.view.View.VISIBLE
                    }
                } catch (e: Exception) {
                    Log.e(TAG, "Error extracting video preview", e)
                }
            }
        } else {
            Toast.makeText(this, getString(R.string.error_file_not_found), Toast.LENGTH_SHORT).show()
        }
    }
    
    private fun getVideoPath(uri: Uri): String? {
        val projection = arrayOf(MediaStore.Video.Media.DATA)
        contentResolver.query(uri, projection, null, null, null)?.use { cursor ->
            if (cursor.moveToFirst()) {
                val columnIndex = cursor.getColumnIndexOrThrow(MediaStore.Video.Media.DATA)
                return cursor.getString(columnIndex)
            }
        }
        return null
    }
    
    private fun createWidget() {
        val videoPath = selectedVideoPath
        if (videoPath == null) {
            Toast.makeText(this, getString(R.string.no_video_selected), Toast.LENGTH_SHORT).show()
            return
        }
        
        lifecycleScope.launch {
            try {
                // Show loading
                val progressBar = findViewById<android.widget.ProgressBar>(R.id.progressBar)
                val buttonCreateWidget = findViewById<com.google.android.material.button.MaterialButton>(R.id.buttonCreateWidget)
                val buttonSelectVideo = findViewById<com.google.android.material.button.MaterialButton>(R.id.buttonSelectVideo)
                progressBar.visibility = android.view.View.VISIBLE
                buttonCreateWidget.isEnabled = false
                buttonSelectVideo.isEnabled = false
                
                // Extract video metadata
                val frameExtractor = VideoFrameExtractor()
                val metadata = frameExtractor.getVideoMetadata(videoPath)
                
                if (metadata == null) {
                    showError(getString(R.string.error_video_processing))
                    return@launch
                }
                
                // Get widget name from input or use default
                val editTextWidgetName = findViewById<com.google.android.material.textfield.TextInputEditText>(R.id.editTextWidgetName)
                val widgetName = editTextWidgetName.text.toString().ifEmpty {
                    "Video Widget $appWidgetId"
                }
                
                // Create widget
                val widget = Widget(
                    id = appWidgetId,
                    name = widgetName,
                    description = "Duration: ${metadata.duration / 1000}s",
                    videoPath = videoPath,
                    framerate = metadata.frameRate,
                    mediaType = "video"
                )
                
                // Add widget to repository
                repository.addOrUpdateWidget(widget)
                
                // Update the widget
                val appWidgetManager = AppWidgetManager.getInstance(this@WidgetConfigurationActivity)
                EnhancedVideoWidget.updateAppWidget(
                    this@WidgetConfigurationActivity,
                    appWidgetManager,
                    appWidgetId,
                    widget
                )
                
                // Set result and finish
                val resultValue = Intent().apply {
                    putExtra(AppWidgetManager.EXTRA_APPWIDGET_ID, appWidgetId)
                }
                setResult(RESULT_OK, resultValue)
                
                Toast.makeText(this@WidgetConfigurationActivity, getString(R.string.widget_created), Toast.LENGTH_SHORT).show()
                finish()
                
            } catch (e: Exception) {
                Log.e(TAG, "Error creating widget", e)
                showError(getString(R.string.error_widget_creation))
            } finally {
                // Hide loading
                val progressBar = findViewById<android.widget.ProgressBar>(R.id.progressBar)
                val buttonCreateWidget = findViewById<com.google.android.material.button.MaterialButton>(R.id.buttonCreateWidget)
                val buttonSelectVideo = findViewById<com.google.android.material.button.MaterialButton>(R.id.buttonSelectVideo)
                progressBar.visibility = android.view.View.GONE
                buttonCreateWidget.isEnabled = true
                buttonSelectVideo.isEnabled = true
            }
        }
    }
    
    private fun showError(message: String) {
        Toast.makeText(this, message, Toast.LENGTH_LONG).show()
    }
    
    override fun onSupportNavigateUp(): Boolean {
        finish()
        return true
    }
}
