<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_main" modulePackage="com.enhanced.videowidget" filePath="app\src\main\res\layout\activity_main.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.coordinatorlayout.widget.CoordinatorLayout"><Targets><Target tag="layout/activity_main_0" view="androidx.coordinatorlayout.widget.CoordinatorLayout"><Expressions/><location startLine="1" startOffset="0" endLine="96" endOffset="53"/></Target><Target id="@+id/toolbar" view="androidx.appcompat.widget.Toolbar"><Expressions/><location startLine="13" startOffset="8" endLine="18" endOffset="66"/></Target><Target id="@+id/recyclerViewWidgets" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="28" startOffset="8" endLine="34" endOffset="50"/></Target><Target id="@+id/textViewNoWidgets" view="LinearLayout"><Expressions/><location startLine="37" startOffset="8" endLine="72" endOffset="22"/></Target><Target id="@+id/progressBar" view="ProgressBar"><Expressions/><location startLine="75" startOffset="8" endLine="81" endOffset="57"/></Target><Target id="@+id/fabAddWidget" view="com.google.android.material.floatingactionbutton.FloatingActionButton"><Expressions/><location startLine="86" startOffset="4" endLine="94" endOffset="41"/></Target></Targets></Layout>