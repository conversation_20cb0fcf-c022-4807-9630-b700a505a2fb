package com.enhanced.videowidget.ui.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.enhanced.videowidget.R
import com.enhanced.videowidget.data.model.Widget
import android.widget.ImageButton
import android.widget.ImageView
import android.widget.TextView

/**
 * Adapter for displaying widget list in RecyclerView
 */
class WidgetListAdapter(
    private val onWidgetClick: (Widget) -> Unit,
    private val onDeleteClick: (Widget) -> Unit
) : ListAdapter<Widget, WidgetListAdapter.WidgetViewHolder>(WidgetDiffCallback()) {
    
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): WidgetViewHolder {
        val view = LayoutInflater.from(parent.context)
            .inflate(R.layout.item_widget, parent, false)
        return WidgetViewHolder(view)
    }
    
    override fun onBindViewHolder(holder: WidgetViewHolder, position: Int) {
        holder.bind(getItem(position))
    }
    
    inner class WidgetViewHolder(
        itemView: android.view.View
    ) : RecyclerView.ViewHolder(itemView) {

        private val textViewName: TextView = itemView.findViewById(R.id.textViewName)
        private val textViewDescription: TextView = itemView.findViewById(R.id.textViewDescription)
        private val textViewStatus: TextView = itemView.findViewById(R.id.textViewStatus)
        private val textViewFrameInfo: TextView = itemView.findViewById(R.id.textViewFrameInfo)
        private val imageViewPreview: ImageView = itemView.findViewById(R.id.imageViewPreview)
        private val imageViewStatus: ImageView = itemView.findViewById(R.id.imageViewStatus)
        private val buttonPlayPause: ImageButton = itemView.findViewById(R.id.buttonPlayPause)
        private val buttonDelete: ImageButton = itemView.findViewById(R.id.buttonDelete)

        fun bind(widget: Widget) {
            // Set widget info
            textViewName.text = widget.name
            textViewDescription.text = widget.description

            // Set playback status
            val statusText = if (widget.isPaused) {
                itemView.context.getString(R.string.paused_status)
            } else {
                itemView.context.getString(R.string.playing_status)
            }
            textViewStatus.text = statusText

            // Set status icon
            val statusIcon = if (widget.isPaused) R.drawable.ic_play else R.drawable.ic_pause
            imageViewStatus.setImageResource(statusIcon)

            // Set frame info
            textViewFrameInfo.text = "${widget.currentFrameIndex}/${widget.totalFrames}"

            // Set preview image
            widget.firstFrame?.let { bitmap ->
                imageViewPreview.setImageBitmap(bitmap)
            } ?: run {
                imageViewPreview.setImageResource(R.drawable.placeholder_video)
            }

            // Set play/pause button
            val playPauseIcon = if (widget.isPaused) R.drawable.ic_play else R.drawable.ic_pause
            buttonPlayPause.setImageResource(playPauseIcon)

            // Set click listeners
            itemView.setOnClickListener { onWidgetClick(widget) }
            buttonPlayPause.setOnClickListener { onWidgetClick(widget) }
            buttonDelete.setOnClickListener { onDeleteClick(widget) }
        }
    }
    
    private class WidgetDiffCallback : DiffUtil.ItemCallback<Widget>() {
        override fun areItemsTheSame(oldItem: Widget, newItem: Widget): Boolean {
            return oldItem.id == newItem.id
        }
        
        override fun areContentsTheSame(oldItem: Widget, newItem: Widget): Boolean {
            return oldItem == newItem
        }
    }
}
