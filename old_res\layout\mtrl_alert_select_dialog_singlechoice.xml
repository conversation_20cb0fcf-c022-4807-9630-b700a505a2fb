<?xml version="1.0" encoding="utf-8"?>
<CheckedTextView xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:ellipsize="marquee"
    android:gravity="center"
    android:id="@android:id/text1"
    android:paddingLeft="@dimen/abc_select_dialog_padding_start_material"
    android:paddingRight="?attr/dialogPreferredPadding"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:minHeight="?attr/listPreferredItemHeightSmall"
    android:drawablePadding="20dp"
    android:textAlignment="viewStart"
    android:paddingStart="@dimen/abc_select_dialog_padding_start_material"
    android:paddingEnd="?attr/dialogPreferredPadding"
    app:drawableLeftCompat="?android:attr/listChoiceIndicatorSingle"
    app:drawableStartCompat="?android:attr/listChoiceIndicatorSingle"/>
