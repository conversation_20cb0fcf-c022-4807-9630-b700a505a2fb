<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@color/background"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <TextView
        android:textSize="36sp"
        android:textColor="@color/textColor"
        android:id="@+id/headerTextView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/video_widgets"
        android:fontFamily="@font/samsungsharpsans_bold"
        app:layout_constraintBottom_toTopOf="@+id/videoView"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>
    <VideoView
        android:id="@+id/videoView"
        android:background="@android:color/transparent"
        android:layout_width="wrap_content"
        android:layout_height="460dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>
    <TextView
        android:textColor="@color/textColor"
        android:id="@+id/textView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/create_widget"
        app:layout_constraintBottom_toTopOf="@+id/shareBtn"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/videoView"/>
    <Button
        android:id="@+id/rateBtn"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="8dp"
        android:text="Rate"
        android:layout_marginStart="8dp"
        android:backgroundTint="@color/blue"
        app:icon="@drawable/ic_star"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"/>
    <Button
        android:id="@+id/shareBtn"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="8dp"
        android:text="Share"
        android:layout_marginStart="8dp"
        android:backgroundTint="@color/blue"
        app:icon="@drawable/ic_share"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toEndOf="@+id/rateBtn"/>
</androidx.constraintlayout.widget.ConstraintLayout>
