<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@color/background"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <TextView
        android:textSize="44sp"
        android:textColor="@color/textColor"
        android:id="@+id/headerTextView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="88dp"
        android:text="@string/video_widgets"
        android:fontFamily="@font/samsungsharpsans_bold"
        android:translationZ="1dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>
    <ImageView
        android:id="@+id/imageView"
        android:background="@drawable/tilted_welcome_phone"
        android:layout_width="0dp"
        android:layout_height="500dp"
        android:layout_marginTop="60dp"
        android:layout_marginRight="40dp"
        android:scaleType="centerCrop"
        android:clipToOutline="true"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintDimensionRatio="2547:2416"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>
    <androidx.appcompat.widget.AppCompatButton
        android:textSize="20sp"
        android:textStyle="bold"
        android:textColor="@color/white"
        android:id="@+id/continue_button"
        android:background="@drawable/btn_background"
        android:layout_width="match_parent"
        android:layout_height="64dp"
        android:layout_marginLeft="18dp"
        android:layout_marginRight="18dp"
        android:layout_marginBottom="16dp"
        android:text="Continue"
        android:textAllCaps="false"
        android:layout_marginHorizontal="18dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"/>
</androidx.constraintlayout.widget.ConstraintLayout>
