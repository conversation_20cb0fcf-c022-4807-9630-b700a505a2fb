// Generated by view binder compiler. Do not edit!
package com.enhanced.videowidget.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.Toolbar;
import androidx.coordinatorlayout.widget.CoordinatorLayout;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.enhanced.videowidget.R;
import com.google.android.material.floatingactionbutton.FloatingActionButton;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityMainBinding implements ViewBinding {
  @NonNull
  private final CoordinatorLayout rootView;

  @NonNull
  public final FloatingActionButton fabAddWidget;

  @NonNull
  public final ProgressBar progressBar;

  @NonNull
  public final RecyclerView recyclerViewWidgets;

  @NonNull
  public final LinearLayout textViewNoWidgets;

  @NonNull
  public final Toolbar toolbar;

  private ActivityMainBinding(@NonNull CoordinatorLayout rootView,
      @NonNull FloatingActionButton fabAddWidget, @NonNull ProgressBar progressBar,
      @NonNull RecyclerView recyclerViewWidgets, @NonNull LinearLayout textViewNoWidgets,
      @NonNull Toolbar toolbar) {
    this.rootView = rootView;
    this.fabAddWidget = fabAddWidget;
    this.progressBar = progressBar;
    this.recyclerViewWidgets = recyclerViewWidgets;
    this.textViewNoWidgets = textViewNoWidgets;
    this.toolbar = toolbar;
  }

  @Override
  @NonNull
  public CoordinatorLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityMainBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityMainBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_main, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityMainBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.fabAddWidget;
      FloatingActionButton fabAddWidget = ViewBindings.findChildViewById(rootView, id);
      if (fabAddWidget == null) {
        break missingId;
      }

      id = R.id.progressBar;
      ProgressBar progressBar = ViewBindings.findChildViewById(rootView, id);
      if (progressBar == null) {
        break missingId;
      }

      id = R.id.recyclerViewWidgets;
      RecyclerView recyclerViewWidgets = ViewBindings.findChildViewById(rootView, id);
      if (recyclerViewWidgets == null) {
        break missingId;
      }

      id = R.id.textViewNoWidgets;
      LinearLayout textViewNoWidgets = ViewBindings.findChildViewById(rootView, id);
      if (textViewNoWidgets == null) {
        break missingId;
      }

      id = R.id.toolbar;
      Toolbar toolbar = ViewBindings.findChildViewById(rootView, id);
      if (toolbar == null) {
        break missingId;
      }

      return new ActivityMainBinding((CoordinatorLayout) rootView, fabAddWidget, progressBar,
          recyclerViewWidgets, textViewNoWidgets, toolbar);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
