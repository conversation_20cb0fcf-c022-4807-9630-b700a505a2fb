<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@color/background"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:backgroundTint="@color/background">
    <TextView
        android:textSize="32sp"
        android:textStyle="bold"
        android:textColor="@color/textColor"
        android:id="@+id/headerText"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="44dp"
        android:text="Add Widget"
        android:layout_marginStart="16dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>
    <androidx.cardview.widget.CardView
        android:id="@+id/thumbnailCard"
        android:layout_width="180dp"
        android:layout_height="180dp"
        android:layout_marginBottom="48dp"
        app:cardCornerRadius="20dp"
        app:cardElevation="0dp"
        app:layout_constraintBottom_toTopOf="@+id/add_button"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">
        <ImageView
            android:id="@+id/thumbnail"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:src="@drawable/placeholder"
            android:scaleType="centerCrop"/>
    </androidx.cardview.widget.CardView>
    <androidx.appcompat.widget.AppCompatButton
        android:textSize="18sp"
        android:textStyle="bold"
        android:textColor="@color/white"
        android:id="@+id/uploadVideo"
        android:background="@drawable/btn_background"
        android:layout_width="match_parent"
        android:layout_height="48dp"
        android:layout_marginLeft="80dp"
        android:layout_marginTop="24dp"
        android:layout_marginRight="80dp"
        android:text="Upload Media"
        android:textAllCaps="false"
        android:layout_marginHorizontal="80dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/thumbnailCard"/>
    <androidx.appcompat.widget.AppCompatButton
        android:textSize="20sp"
        android:textStyle="bold"
        android:textColor="#bfbfbf"
        android:id="@+id/add_button"
        android:background="@drawable/btn_disabled_background"
        android:layout_width="match_parent"
        android:layout_height="64dp"
        android:layout_marginLeft="18dp"
        android:layout_marginRight="18dp"
        android:layout_marginBottom="16dp"
        android:text="@string/add_widget"
        android:textAllCaps="false"
        android:layout_marginHorizontal="18dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"/>
</androidx.constraintlayout.widget.ConstraintLayout>
