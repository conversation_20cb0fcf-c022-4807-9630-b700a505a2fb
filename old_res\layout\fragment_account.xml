<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@color/background"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:backgroundTint="@color/background">
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recyclerViewWidgets"
        android:paddingLeft="4dp"
        android:paddingRight="4dp"
        android:paddingBottom="16dp"
        android:clipChildren="true"
        android:clipToPadding="true"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginTop="16dp"
        android:paddingHorizontal="4dp"
        app:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/headerText"
        app:spanCount="2"/>
    <TextView
        android:textSize="32sp"
        android:textStyle="bold"
        android:textColor="@color/textColor"
        android:id="@+id/headerText"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="44dp"
        android:text="@string/widget_list"
        android:layout_marginStart="16dp"
        android:elevation="10dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>
    <com.google.android.material.button.MaterialButton
        android:id="@+id/moreBtn"
        android:background="?attr/selectableItemBackgroundBorderless"
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:contentDescription="@string/more"
        android:layout_marginEnd="15dp"
        app:icon="@drawable/ic_more"
        app:iconGravity="textStart"
        app:iconPadding="-4dp"
        app:iconSize="26dp"
        app:iconTint="#bcbcbc"
        app:layout_constraintBottom_toBottomOf="@+id/headerText"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/headerText"
        style="@style/Widget.MaterialComponents.Button.Icon"/>
    <TextView
        android:textColor="#bcbcbc"
        android:id="@+id/emptyListText"
        android:visibility="gone"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/empty_list"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>
</androidx.constraintlayout.widget.ConstraintLayout>
