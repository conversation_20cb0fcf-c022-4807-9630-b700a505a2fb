<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_gravity="center"
    android:orientation="vertical"
    android:background="@drawable/rounded_background"
    android:padding="32dp"
    android:layout_width="300dp"
    android:layout_height="wrap_content">
    <TextView
        android:textSize="20sp"
        android:textColor="@color/textColor"
        android:id="@+id/titleText"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="24dp"
        android:text="Theme"
        android:fontFamily="sans-serif-medium"/>
    <RadioGroup
        android:orientation="vertical"
        android:id="@+id/themeRadioGroup"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="12dp"
        android:layout_marginBottom="12dp">
        <RadioButton
            android:id="@+id/systemDefault"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="12dp"
            android:text="System Default"/>
        <RadioButton
            android:id="@+id/lightMode"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="12dp"
            android:text="Light mode"/>
        <RadioButton
            android:id="@+id/darkMode"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Dark mode"/>
    </RadioGroup>
</LinearLayout>
