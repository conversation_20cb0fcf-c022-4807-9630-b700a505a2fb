<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:gravity="center"
    android:orientation="vertical"
    android:background="@drawable/round_outline"
    android:padding="24dp"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:backgroundTint="@color/background">
    <TextView
        android:textSize="24sp"
        android:textStyle="bold"
        android:textColor="@color/textColor"
        android:id="@+id/dialog_title"
        android:paddingBottom="12dp"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Remove Ads"
        android:textAlignment="center"/>
    <TextView
        android:textSize="16sp"
        android:textColor="@color/textColor"
        android:id="@+id/dialog_description"
        android:paddingBottom="16dp"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Enjoy an ad-free experience by purchasing the ad removal. Unlock a smoother, uninterrupted app experience by removing ads."
        android:lineSpacingExtra="4dp"/>
    <TextView
        android:textSize="16sp"
        android:textStyle="bold"
        android:textColor="@color/blue"
        android:id="@+id/link_tos"
        android:paddingBottom="8dp"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Terms of Service"
        android:drawablePadding="8dp"
        android:drawableStart="@drawable/ic_link"
        android:drawableTint="@color/textColor"/>
    <TextView
        android:textSize="16sp"
        android:textStyle="bold"
        android:textColor="@color/blue"
        android:id="@+id/link_privacy"
        android:paddingBottom="24dp"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Privacy Policy"
        android:drawablePadding="8dp"
        android:drawableStart="@drawable/ic_link"
        android:drawableTint="@color/textColor"/>
    <com.google.android.material.button.MaterialButton
        android:textSize="18sp"
        android:textStyle="bold"
        android:textColor="@color/white"
        android:id="@+id/continue_purchase_button"
        android:layout_width="match_parent"
        android:layout_height="48dp"
        android:text="Continue"
        android:backgroundTint="@color/blue"/>
</LinearLayout>
