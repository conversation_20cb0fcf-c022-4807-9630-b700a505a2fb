// Generated by view binder compiler. Do not edit!
package com.enhanced.videowidget.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageButton;
import android.widget.ImageView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.enhanced.videowidget.R;
import com.google.android.material.card.MaterialCardView;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemWidgetBinding implements ViewBinding {
  @NonNull
  private final MaterialCardView rootView;

  @NonNull
  public final ImageButton buttonDelete;

  @NonNull
  public final ImageButton buttonPlayPause;

  @NonNull
  public final ImageView imageViewPreview;

  @NonNull
  public final ImageView imageViewStatus;

  @NonNull
  public final TextView textViewDescription;

  @NonNull
  public final TextView textViewFrameInfo;

  @NonNull
  public final TextView textViewName;

  @NonNull
  public final TextView textViewStatus;

  private ItemWidgetBinding(@NonNull MaterialCardView rootView, @NonNull ImageButton buttonDelete,
      @NonNull ImageButton buttonPlayPause, @NonNull ImageView imageViewPreview,
      @NonNull ImageView imageViewStatus, @NonNull TextView textViewDescription,
      @NonNull TextView textViewFrameInfo, @NonNull TextView textViewName,
      @NonNull TextView textViewStatus) {
    this.rootView = rootView;
    this.buttonDelete = buttonDelete;
    this.buttonPlayPause = buttonPlayPause;
    this.imageViewPreview = imageViewPreview;
    this.imageViewStatus = imageViewStatus;
    this.textViewDescription = textViewDescription;
    this.textViewFrameInfo = textViewFrameInfo;
    this.textViewName = textViewName;
    this.textViewStatus = textViewStatus;
  }

  @Override
  @NonNull
  public MaterialCardView getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemWidgetBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemWidgetBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_widget, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemWidgetBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.buttonDelete;
      ImageButton buttonDelete = ViewBindings.findChildViewById(rootView, id);
      if (buttonDelete == null) {
        break missingId;
      }

      id = R.id.buttonPlayPause;
      ImageButton buttonPlayPause = ViewBindings.findChildViewById(rootView, id);
      if (buttonPlayPause == null) {
        break missingId;
      }

      id = R.id.imageViewPreview;
      ImageView imageViewPreview = ViewBindings.findChildViewById(rootView, id);
      if (imageViewPreview == null) {
        break missingId;
      }

      id = R.id.imageViewStatus;
      ImageView imageViewStatus = ViewBindings.findChildViewById(rootView, id);
      if (imageViewStatus == null) {
        break missingId;
      }

      id = R.id.textViewDescription;
      TextView textViewDescription = ViewBindings.findChildViewById(rootView, id);
      if (textViewDescription == null) {
        break missingId;
      }

      id = R.id.textViewFrameInfo;
      TextView textViewFrameInfo = ViewBindings.findChildViewById(rootView, id);
      if (textViewFrameInfo == null) {
        break missingId;
      }

      id = R.id.textViewName;
      TextView textViewName = ViewBindings.findChildViewById(rootView, id);
      if (textViewName == null) {
        break missingId;
      }

      id = R.id.textViewStatus;
      TextView textViewStatus = ViewBindings.findChildViewById(rootView, id);
      if (textViewStatus == null) {
        break missingId;
      }

      return new ItemWidgetBinding((MaterialCardView) rootView, buttonDelete, buttonPlayPause,
          imageViewPreview, imageViewStatus, textViewDescription, textViewFrameInfo, textViewName,
          textViewStatus);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
