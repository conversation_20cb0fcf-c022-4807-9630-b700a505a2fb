package com.enhanced.videowidget.utils

import android.util.Log

/**
 * Centralized logging utility with configurable debug levels
 */
object AppLogger {
    
    // Debug configuration - set to true to enable detailed logging
    // Change these values to control logging levels
    private const val DEBUG_ENABLED = true
    private const val VERBOSE_ENABLED = true

    // Quick method to check if debug logging is enabled
    fun isDebugEnabled() = DEBUG_ENABLED
    fun isVerboseEnabled() = VERBOSE_ENABLED
    
    // Log tags for different components
    const val TAG_WIDGET = "EnhancedWidget"
    const val TAG_VIDEO = "VideoProcessor"
    const val TAG_FRAMES = "FrameExtractor"
    const val TAG_WORKER = "WidgetWorker"
    const val TAG_CONFIG = "WidgetConfig"
    const val TAG_REPOSITORY = "WidgetRepo"
    const val TAG_SERVICE = "WidgetService"
    
    fun d(tag: String, message: String) {
        if (DEBUG_ENABLED) {
            Log.d(tag, message)
        }
    }
    
    fun v(tag: String, message: String) {
        if (VERBOSE_ENABLED) {
            Log.v(tag, message)
        }
    }
    
    fun i(tag: String, message: String) {
        Log.i(tag, message)
    }
    
    fun w(tag: String, message: String) {
        Log.w(tag, message)
    }
    
    fun w(tag: String, message: String, throwable: Throwable) {
        Log.w(tag, message, throwable)
    }
    
    fun e(tag: String, message: String) {
        Log.e(tag, message)
    }
    
    fun e(tag: String, message: String, throwable: Throwable) {
        Log.e(tag, message, throwable)
    }
    
    // Specialized logging methods for different components
    
    fun logVideoSelection(videoPath: String?, videoUri: String?) {
        d(TAG_CONFIG, "=== VIDEO SELECTION ===")
        d(TAG_CONFIG, "Video Path: $videoPath")
        d(TAG_CONFIG, "Video URI: $videoUri")
    }
    
    fun logVideoMetadata(duration: Long, frameRate: Float, width: Int, height: Int) {
        d(TAG_VIDEO, "=== VIDEO METADATA ===")
        d(TAG_VIDEO, "Duration: ${duration}ms (${duration/1000}s)")
        d(TAG_VIDEO, "Frame Rate: $frameRate fps")
        d(TAG_VIDEO, "Resolution: ${width}x${height}")
    }
    
    fun logFrameExtraction(frameIndex: Int, totalFrames: Int, success: Boolean) {
        v(TAG_FRAMES, "Frame $frameIndex/$totalFrames - Success: $success")
    }
    
    fun logWidgetUpdate(widgetId: Int, frameIndex: Int, isPlaying: Boolean) {
        v(TAG_WIDGET, "Widget $widgetId - Frame: $frameIndex, Playing: $isPlaying")
    }
    
    fun logWorkerStart(widgetId: Int) {
        i(TAG_WORKER, "=== WORKER STARTED for Widget $widgetId ===")
    }
    
    fun logWorkerEnd(widgetId: Int, success: Boolean) {
        i(TAG_WORKER, "=== WORKER ENDED for Widget $widgetId - Success: $success ===")
    }
    
    fun logError(tag: String, operation: String, error: Throwable) {
        e(tag, "ERROR in $operation: ${error.message}", error)
    }
    
    fun logFileOperation(operation: String, path: String, success: Boolean) {
        d(TAG_REPOSITORY, "File $operation: $path - Success: $success")
    }
}
