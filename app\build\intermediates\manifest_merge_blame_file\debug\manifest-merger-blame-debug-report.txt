1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.enhanced.videowidget"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
8-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml
9        android:targetSdkVersion="33" />
9-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml
10
11    <!-- Permissions -->
12    <uses-permission
12-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:7:5-8:38
13        android:name="android.permission.READ_EXTERNAL_STORAGE"
13-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:7:22-77
14        android:maxSdkVersion="32" />
14-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:8:9-35
15    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
15-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:9:5-75
15-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:9:22-72
16    <uses-permission
16-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:10:5-11:38
17        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
17-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:10:22-78
18        android:maxSdkVersion="28" />
18-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:11:9-35
19    <uses-permission android:name="android.permission.INTERNET" />
19-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:12:5-67
19-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:12:22-64
20    <uses-permission android:name="android.permission.WAKE_LOCK" />
20-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:13:5-68
20-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:13:22-65
21    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
21-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:14:5-77
21-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:14:22-74
22    <uses-permission android:name="android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS" />
22-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:15:5-95
22-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:15:22-92
23
24    <!-- Features -->
25    <uses-feature
25-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:18:5-20:35
26        android:name="android.software.app_widgets"
26-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:19:9-52
27        android:required="true" />
27-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:20:9-32
28
29    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
29-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:24:5-79
29-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:24:22-76
30    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
30-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:25:5-81
30-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:25:22-78
31
32    <permission
32-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\transforms-3\6fe3e64a80094b4316c7006f0bcdcf47\transformed\core-1.10.1\AndroidManifest.xml:22:5-24:47
33        android:name="com.enhanced.videowidget.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
33-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\transforms-3\6fe3e64a80094b4316c7006f0bcdcf47\transformed\core-1.10.1\AndroidManifest.xml:23:9-81
34        android:protectionLevel="signature" />
34-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\transforms-3\6fe3e64a80094b4316c7006f0bcdcf47\transformed\core-1.10.1\AndroidManifest.xml:24:9-44
35
36    <uses-permission android:name="com.enhanced.videowidget.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
36-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\transforms-3\6fe3e64a80094b4316c7006f0bcdcf47\transformed\core-1.10.1\AndroidManifest.xml:26:5-97
36-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\transforms-3\6fe3e64a80094b4316c7006f0bcdcf47\transformed\core-1.10.1\AndroidManifest.xml:26:22-94
37
38    <application
38-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:22:5-84:19
39        android:allowBackup="true"
39-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:23:9-35
40        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
40-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\transforms-3\6fe3e64a80094b4316c7006f0bcdcf47\transformed\core-1.10.1\AndroidManifest.xml:28:18-86
41        android:dataExtractionRules="@xml/data_extraction_rules"
41-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:24:9-65
42        android:debuggable="true"
43        android:extractNativeLibs="false"
44        android:fullBackupContent="@xml/backup_rules"
44-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:25:9-54
45        android:icon="@mipmap/ic_launcher"
45-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:26:9-43
46        android:label="@string/app_name"
46-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:27:9-41
47        android:theme="@style/Theme.EnhancedVideoWidget" >
47-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:28:9-57
48
49        <!-- Main Activity -->
50        <activity
50-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:32:9-40:20
51            android:name="com.enhanced.videowidget.ui.MainActivity"
51-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:33:13-44
52            android:exported="true"
52-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:34:13-36
53            android:theme="@style/Theme.EnhancedVideoWidget" >
53-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:35:13-61
54            <intent-filter>
54-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:36:13-39:29
55                <action android:name="android.intent.action.MAIN" />
55-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:37:17-69
55-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:37:25-66
56
57                <category android:name="android.intent.category.LAUNCHER" />
57-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:38:17-77
57-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:38:27-74
58            </intent-filter>
59        </activity>
60
61        <!-- Widget Configuration Activity -->
62        <activity
62-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:43:9-50:20
63            android:name="com.enhanced.videowidget.ui.WidgetConfigurationActivity"
63-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:44:13-59
64            android:exported="false"
64-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:45:13-37
65            android:theme="@style/Theme.EnhancedVideoWidget.NoActionBar" >
65-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:46:13-73
66            <intent-filter>
66-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:47:13-49:29
67                <action android:name="android.appwidget.action.APPWIDGET_CONFIGURE" />
67-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:48:17-87
67-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:48:25-84
68            </intent-filter>
69        </activity>
70
71        <!-- Enhanced Video Widget Provider -->
72        <receiver
72-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:53:9-65:20
73            android:name="com.enhanced.videowidget.widget.EnhancedVideoWidget"
73-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:54:13-55
74            android:exported="true" >
74-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:55:13-36
75            <intent-filter>
75-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:56:13-61:29
76                <action android:name="android.appwidget.action.APPWIDGET_UPDATE" />
76-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:57:17-84
76-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:57:25-81
77                <action android:name="com.enhanced.videowidget.ACTION_WIDGET_TAP" />
77-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:58:17-85
77-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:58:25-82
78                <action android:name="com.enhanced.videowidget.ACTION_PLAY_PAUSE" />
78-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:59:17-85
78-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:59:25-82
79                <action android:name="com.enhanced.videowidget.ACTION_SETTINGS" />
79-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:60:17-83
79-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:60:25-80
80            </intent-filter>
81
82            <meta-data
82-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:62:13-64:70
83                android:name="android.appwidget.provider"
83-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:63:17-58
84                android:resource="@xml/enhanced_video_widget_info" />
84-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:64:17-67
85        </receiver>
86
87        <!-- Widget Update Service -->
88        <service
88-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:68:9-71:56
89            android:name="com.enhanced.videowidget.service.WidgetUpdateService"
89-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:69:13-56
90            android:exported="false"
90-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:70:13-37
91            android:foregroundServiceType="dataSync" />
91-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:71:13-53
92
93        <!-- Work Manager -->
94        <provider
95            android:name="androidx.startup.InitializationProvider"
95-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:75:13-67
96            android:authorities="com.enhanced.videowidget.androidx-startup"
96-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:76:13-68
97            android:exported="false" >
97-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:77:13-37
98            <meta-data
98-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:79:13-81:52
99                android:name="androidx.work.WorkManagerInitializer"
99-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:80:17-68
100                android:value="androidx.startup" />
100-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:81:17-49
101            <meta-data
101-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\f5bce95adc2736b0739e9bbfa23aa81b\transformed\emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
102                android:name="androidx.emoji2.text.EmojiCompatInitializer"
102-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\f5bce95adc2736b0739e9bbfa23aa81b\transformed\emoji2-1.2.0\AndroidManifest.xml:30:17-75
103                android:value="androidx.startup" />
103-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\f5bce95adc2736b0739e9bbfa23aa81b\transformed\emoji2-1.2.0\AndroidManifest.xml:31:17-49
104            <meta-data
104-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\f79bf1fa26380065745fd6a35eb2733a\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:29:13-31:52
105                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
105-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\f79bf1fa26380065745fd6a35eb2733a\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:30:17-78
106                android:value="androidx.startup" />
106-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\f79bf1fa26380065745fd6a35eb2733a\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:31:17-49
107            <meta-data
107-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\855f345d0f57b580ddb9a53f14817127\transformed\profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
108                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
108-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\855f345d0f57b580ddb9a53f14817127\transformed\profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
109                android:value="androidx.startup" />
109-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\855f345d0f57b580ddb9a53f14817127\transformed\profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
110        </provider>
111
112        <service
112-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:39:9-45:35
113            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
113-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:40:13-88
114            android:directBootAware="false"
114-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:41:13-44
115            android:enabled="@bool/enable_system_alarm_service_default"
115-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:42:13-72
116            android:exported="false" />
116-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:43:13-37
117        <service
117-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:46:9-52:35
118            android:name="androidx.work.impl.background.systemjob.SystemJobService"
118-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:47:13-84
119            android:directBootAware="false"
119-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:48:13-44
120            android:enabled="@bool/enable_system_job_service_default"
120-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:49:13-70
121            android:exported="true"
121-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:50:13-36
122            android:permission="android.permission.BIND_JOB_SERVICE" />
122-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:51:13-69
123        <service
123-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:53:9-59:35
124            android:name="androidx.work.impl.foreground.SystemForegroundService"
124-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:54:13-81
125            android:directBootAware="false"
125-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:55:13-44
126            android:enabled="@bool/enable_system_foreground_service_default"
126-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:56:13-77
127            android:exported="false" />
127-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:57:13-37
128
129        <receiver
129-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:61:9-66:35
130            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
130-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:62:13-88
131            android:directBootAware="false"
131-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:63:13-44
132            android:enabled="true"
132-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:64:13-35
133            android:exported="false" />
133-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:65:13-37
134        <receiver
134-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:67:9-77:20
135            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
135-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:68:13-106
136            android:directBootAware="false"
136-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:69:13-44
137            android:enabled="false"
137-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:70:13-36
138            android:exported="false" >
138-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:71:13-37
139            <intent-filter>
139-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:73:13-76:29
140                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
140-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:74:17-87
140-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:74:25-84
141                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
141-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:75:17-90
141-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:75:25-87
142            </intent-filter>
143        </receiver>
144        <receiver
144-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:78:9-88:20
145            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
145-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:79:13-104
146            android:directBootAware="false"
146-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:80:13-44
147            android:enabled="false"
147-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:81:13-36
148            android:exported="false" >
148-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:82:13-37
149            <intent-filter>
149-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:84:13-87:29
150                <action android:name="android.intent.action.BATTERY_OKAY" />
150-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:85:17-77
150-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:85:25-74
151                <action android:name="android.intent.action.BATTERY_LOW" />
151-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:86:17-76
151-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:86:25-73
152            </intent-filter>
153        </receiver>
154        <receiver
154-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:89:9-99:20
155            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
155-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:90:13-104
156            android:directBootAware="false"
156-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:91:13-44
157            android:enabled="false"
157-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:92:13-36
158            android:exported="false" >
158-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:93:13-37
159            <intent-filter>
159-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:95:13-98:29
160                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
160-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:96:17-83
160-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:96:25-80
161                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
161-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:97:17-82
161-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:97:25-79
162            </intent-filter>
163        </receiver>
164        <receiver
164-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:100:9-109:20
165            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
165-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:101:13-103
166            android:directBootAware="false"
166-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:102:13-44
167            android:enabled="false"
167-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:103:13-36
168            android:exported="false" >
168-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:104:13-37
169            <intent-filter>
169-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:106:13-108:29
170                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
170-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:107:17-79
170-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:107:25-76
171            </intent-filter>
172        </receiver>
173        <receiver
173-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:110:9-121:20
174            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
174-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:111:13-88
175            android:directBootAware="false"
175-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:112:13-44
176            android:enabled="false"
176-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:113:13-36
177            android:exported="false" >
177-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:114:13-37
178            <intent-filter>
178-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:116:13-120:29
179                <action android:name="android.intent.action.BOOT_COMPLETED" />
179-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:117:17-79
179-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:117:25-76
180                <action android:name="android.intent.action.TIME_SET" />
180-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:118:17-73
180-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:118:25-70
181                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
181-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:119:17-81
181-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:119:25-78
182            </intent-filter>
183        </receiver>
184        <receiver
184-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:122:9-131:20
185            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
185-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:123:13-99
186            android:directBootAware="false"
186-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:124:13-44
187            android:enabled="@bool/enable_system_alarm_service_default"
187-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:125:13-72
188            android:exported="false" >
188-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:126:13-37
189            <intent-filter>
189-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:128:13-130:29
190                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
190-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:129:17-98
190-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:129:25-95
191            </intent-filter>
192        </receiver>
193        <receiver
193-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:132:9-142:20
194            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
194-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:133:13-78
195            android:directBootAware="false"
195-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:134:13-44
196            android:enabled="true"
196-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:135:13-35
197            android:exported="true"
197-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:136:13-36
198            android:permission="android.permission.DUMP" >
198-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:137:13-57
199            <intent-filter>
199-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:139:13-141:29
200                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
200-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:140:17-88
200-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:140:25-85
201            </intent-filter>
202        </receiver>
203
204        <service
204-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\891ad734ff703f843ef3afba41c84481\transformed\room-runtime-2.5.0\AndroidManifest.xml:24:9-28:63
205            android:name="androidx.room.MultiInstanceInvalidationService"
205-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\891ad734ff703f843ef3afba41c84481\transformed\room-runtime-2.5.0\AndroidManifest.xml:25:13-74
206            android:directBootAware="true"
206-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\891ad734ff703f843ef3afba41c84481\transformed\room-runtime-2.5.0\AndroidManifest.xml:26:13-43
207            android:exported="false" />
207-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\891ad734ff703f843ef3afba41c84481\transformed\room-runtime-2.5.0\AndroidManifest.xml:27:13-37
208
209        <receiver
209-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\855f345d0f57b580ddb9a53f14817127\transformed\profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
210            android:name="androidx.profileinstaller.ProfileInstallReceiver"
210-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\855f345d0f57b580ddb9a53f14817127\transformed\profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
211            android:directBootAware="false"
211-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\855f345d0f57b580ddb9a53f14817127\transformed\profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
212            android:enabled="true"
212-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\855f345d0f57b580ddb9a53f14817127\transformed\profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
213            android:exported="true"
213-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\855f345d0f57b580ddb9a53f14817127\transformed\profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
214            android:permission="android.permission.DUMP" >
214-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\855f345d0f57b580ddb9a53f14817127\transformed\profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
215            <intent-filter>
215-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\855f345d0f57b580ddb9a53f14817127\transformed\profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
216                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
216-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\855f345d0f57b580ddb9a53f14817127\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
216-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\855f345d0f57b580ddb9a53f14817127\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
217            </intent-filter>
218            <intent-filter>
218-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\855f345d0f57b580ddb9a53f14817127\transformed\profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
219                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
219-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\855f345d0f57b580ddb9a53f14817127\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
219-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\855f345d0f57b580ddb9a53f14817127\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
220            </intent-filter>
221            <intent-filter>
221-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\855f345d0f57b580ddb9a53f14817127\transformed\profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
222                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
222-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\855f345d0f57b580ddb9a53f14817127\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
222-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\855f345d0f57b580ddb9a53f14817127\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
223            </intent-filter>
224            <intent-filter>
224-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\855f345d0f57b580ddb9a53f14817127\transformed\profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
225                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
225-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\855f345d0f57b580ddb9a53f14817127\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
225-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\855f345d0f57b580ddb9a53f14817127\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
226            </intent-filter>
227        </receiver>
228    </application>
229
230</manifest>
