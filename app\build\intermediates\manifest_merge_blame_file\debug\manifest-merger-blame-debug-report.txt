1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.enhanced.videowidget"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
8-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml
9        android:targetSdkVersion="33" />
9-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml
10
11    <!-- Permissions -->
12    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
12-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:7:5-80
12-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:7:22-77
13    <uses-permission
13-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:8:5-9:38
14        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
14-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:8:22-78
15        android:maxSdkVersion="28" />
15-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:9:9-35
16    <uses-permission android:name="android.permission.INTERNET" />
16-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:10:5-67
16-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:10:22-64
17    <uses-permission android:name="android.permission.WAKE_LOCK" />
17-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:11:5-68
17-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:11:22-65
18    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
18-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:12:5-77
18-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:12:22-74
19    <uses-permission android:name="android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS" />
19-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:13:5-95
19-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:13:22-92
20
21    <!-- Features -->
22    <uses-feature
22-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:16:5-18:35
23        android:name="android.software.app_widgets"
23-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:17:9-52
24        android:required="true" />
24-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:18:9-32
25
26    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
26-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:24:5-79
26-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:24:22-76
27    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
27-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:25:5-81
27-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:25:22-78
28
29    <permission
29-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\transforms-3\6fe3e64a80094b4316c7006f0bcdcf47\transformed\core-1.10.1\AndroidManifest.xml:22:5-24:47
30        android:name="com.enhanced.videowidget.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
30-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\transforms-3\6fe3e64a80094b4316c7006f0bcdcf47\transformed\core-1.10.1\AndroidManifest.xml:23:9-81
31        android:protectionLevel="signature" />
31-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\transforms-3\6fe3e64a80094b4316c7006f0bcdcf47\transformed\core-1.10.1\AndroidManifest.xml:24:9-44
32
33    <uses-permission android:name="com.enhanced.videowidget.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
33-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\transforms-3\6fe3e64a80094b4316c7006f0bcdcf47\transformed\core-1.10.1\AndroidManifest.xml:26:5-97
33-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\transforms-3\6fe3e64a80094b4316c7006f0bcdcf47\transformed\core-1.10.1\AndroidManifest.xml:26:22-94
34
35    <application
35-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:20:5-82:19
36        android:allowBackup="true"
36-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:21:9-35
37        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
37-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\transforms-3\6fe3e64a80094b4316c7006f0bcdcf47\transformed\core-1.10.1\AndroidManifest.xml:28:18-86
38        android:dataExtractionRules="@xml/data_extraction_rules"
38-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:22:9-65
39        android:debuggable="true"
40        android:extractNativeLibs="false"
41        android:fullBackupContent="@xml/backup_rules"
41-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:23:9-54
42        android:icon="@mipmap/ic_launcher"
42-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:24:9-43
43        android:label="@string/app_name"
43-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:25:9-41
44        android:theme="@style/Theme.EnhancedVideoWidget" >
44-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:26:9-57
45
46        <!-- Main Activity -->
47        <activity
47-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:30:9-38:20
48            android:name="com.enhanced.videowidget.ui.MainActivity"
48-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:31:13-44
49            android:exported="true"
49-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:32:13-36
50            android:theme="@style/Theme.EnhancedVideoWidget" >
50-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:33:13-61
51            <intent-filter>
51-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:34:13-37:29
52                <action android:name="android.intent.action.MAIN" />
52-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:35:17-69
52-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:35:25-66
53
54                <category android:name="android.intent.category.LAUNCHER" />
54-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:36:17-77
54-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:36:27-74
55            </intent-filter>
56        </activity>
57
58        <!-- Widget Configuration Activity -->
59        <activity
59-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:41:9-48:20
60            android:name="com.enhanced.videowidget.ui.WidgetConfigurationActivity"
60-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:42:13-59
61            android:exported="false"
61-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:43:13-37
62            android:theme="@style/Theme.EnhancedVideoWidget.NoActionBar" >
62-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:44:13-73
63            <intent-filter>
63-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:45:13-47:29
64                <action android:name="android.appwidget.action.APPWIDGET_CONFIGURE" />
64-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:46:17-87
64-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:46:25-84
65            </intent-filter>
66        </activity>
67
68        <!-- Enhanced Video Widget Provider -->
69        <receiver
69-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:51:9-63:20
70            android:name="com.enhanced.videowidget.widget.EnhancedVideoWidget"
70-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:52:13-55
71            android:exported="true" >
71-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:53:13-36
72            <intent-filter>
72-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:54:13-59:29
73                <action android:name="android.appwidget.action.APPWIDGET_UPDATE" />
73-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:55:17-84
73-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:55:25-81
74                <action android:name="com.enhanced.videowidget.ACTION_WIDGET_TAP" />
74-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:56:17-85
74-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:56:25-82
75                <action android:name="com.enhanced.videowidget.ACTION_PLAY_PAUSE" />
75-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:57:17-85
75-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:57:25-82
76                <action android:name="com.enhanced.videowidget.ACTION_SETTINGS" />
76-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:58:17-83
76-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:58:25-80
77            </intent-filter>
78
79            <meta-data
79-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:60:13-62:70
80                android:name="android.appwidget.provider"
80-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:61:17-58
81                android:resource="@xml/enhanced_video_widget_info" />
81-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:62:17-67
82        </receiver>
83
84        <!-- Widget Update Service -->
85        <service
85-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:66:9-69:56
86            android:name="com.enhanced.videowidget.service.WidgetUpdateService"
86-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:67:13-56
87            android:exported="false"
87-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:68:13-37
88            android:foregroundServiceType="dataSync" />
88-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:69:13-53
89
90        <!-- Work Manager -->
91        <provider
92            android:name="androidx.startup.InitializationProvider"
92-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:73:13-67
93            android:authorities="com.enhanced.videowidget.androidx-startup"
93-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:74:13-68
94            android:exported="false" >
94-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:75:13-37
95            <meta-data
95-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:77:13-79:52
96                android:name="androidx.work.WorkManagerInitializer"
96-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:78:17-68
97                android:value="androidx.startup" />
97-->D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:79:17-49
98            <meta-data
98-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\f5bce95adc2736b0739e9bbfa23aa81b\transformed\emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
99                android:name="androidx.emoji2.text.EmojiCompatInitializer"
99-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\f5bce95adc2736b0739e9bbfa23aa81b\transformed\emoji2-1.2.0\AndroidManifest.xml:30:17-75
100                android:value="androidx.startup" />
100-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\f5bce95adc2736b0739e9bbfa23aa81b\transformed\emoji2-1.2.0\AndroidManifest.xml:31:17-49
101            <meta-data
101-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\f79bf1fa26380065745fd6a35eb2733a\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:29:13-31:52
102                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
102-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\f79bf1fa26380065745fd6a35eb2733a\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:30:17-78
103                android:value="androidx.startup" />
103-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\f79bf1fa26380065745fd6a35eb2733a\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:31:17-49
104            <meta-data
104-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\855f345d0f57b580ddb9a53f14817127\transformed\profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
105                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
105-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\855f345d0f57b580ddb9a53f14817127\transformed\profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
106                android:value="androidx.startup" />
106-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\855f345d0f57b580ddb9a53f14817127\transformed\profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
107        </provider>
108
109        <service
109-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:39:9-45:35
110            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
110-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:40:13-88
111            android:directBootAware="false"
111-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:41:13-44
112            android:enabled="@bool/enable_system_alarm_service_default"
112-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:42:13-72
113            android:exported="false" />
113-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:43:13-37
114        <service
114-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:46:9-52:35
115            android:name="androidx.work.impl.background.systemjob.SystemJobService"
115-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:47:13-84
116            android:directBootAware="false"
116-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:48:13-44
117            android:enabled="@bool/enable_system_job_service_default"
117-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:49:13-70
118            android:exported="true"
118-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:50:13-36
119            android:permission="android.permission.BIND_JOB_SERVICE" />
119-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:51:13-69
120        <service
120-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:53:9-59:35
121            android:name="androidx.work.impl.foreground.SystemForegroundService"
121-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:54:13-81
122            android:directBootAware="false"
122-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:55:13-44
123            android:enabled="@bool/enable_system_foreground_service_default"
123-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:56:13-77
124            android:exported="false" />
124-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:57:13-37
125
126        <receiver
126-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:61:9-66:35
127            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
127-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:62:13-88
128            android:directBootAware="false"
128-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:63:13-44
129            android:enabled="true"
129-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:64:13-35
130            android:exported="false" />
130-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:65:13-37
131        <receiver
131-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:67:9-77:20
132            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
132-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:68:13-106
133            android:directBootAware="false"
133-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:69:13-44
134            android:enabled="false"
134-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:70:13-36
135            android:exported="false" >
135-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:71:13-37
136            <intent-filter>
136-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:73:13-76:29
137                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
137-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:74:17-87
137-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:74:25-84
138                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
138-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:75:17-90
138-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:75:25-87
139            </intent-filter>
140        </receiver>
141        <receiver
141-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:78:9-88:20
142            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
142-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:79:13-104
143            android:directBootAware="false"
143-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:80:13-44
144            android:enabled="false"
144-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:81:13-36
145            android:exported="false" >
145-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:82:13-37
146            <intent-filter>
146-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:84:13-87:29
147                <action android:name="android.intent.action.BATTERY_OKAY" />
147-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:85:17-77
147-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:85:25-74
148                <action android:name="android.intent.action.BATTERY_LOW" />
148-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:86:17-76
148-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:86:25-73
149            </intent-filter>
150        </receiver>
151        <receiver
151-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:89:9-99:20
152            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
152-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:90:13-104
153            android:directBootAware="false"
153-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:91:13-44
154            android:enabled="false"
154-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:92:13-36
155            android:exported="false" >
155-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:93:13-37
156            <intent-filter>
156-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:95:13-98:29
157                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
157-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:96:17-83
157-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:96:25-80
158                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
158-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:97:17-82
158-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:97:25-79
159            </intent-filter>
160        </receiver>
161        <receiver
161-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:100:9-109:20
162            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
162-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:101:13-103
163            android:directBootAware="false"
163-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:102:13-44
164            android:enabled="false"
164-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:103:13-36
165            android:exported="false" >
165-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:104:13-37
166            <intent-filter>
166-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:106:13-108:29
167                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
167-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:107:17-79
167-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:107:25-76
168            </intent-filter>
169        </receiver>
170        <receiver
170-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:110:9-121:20
171            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
171-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:111:13-88
172            android:directBootAware="false"
172-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:112:13-44
173            android:enabled="false"
173-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:113:13-36
174            android:exported="false" >
174-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:114:13-37
175            <intent-filter>
175-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:116:13-120:29
176                <action android:name="android.intent.action.BOOT_COMPLETED" />
176-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:117:17-79
176-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:117:25-76
177                <action android:name="android.intent.action.TIME_SET" />
177-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:118:17-73
177-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:118:25-70
178                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
178-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:119:17-81
178-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:119:25-78
179            </intent-filter>
180        </receiver>
181        <receiver
181-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:122:9-131:20
182            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
182-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:123:13-99
183            android:directBootAware="false"
183-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:124:13-44
184            android:enabled="@bool/enable_system_alarm_service_default"
184-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:125:13-72
185            android:exported="false" >
185-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:126:13-37
186            <intent-filter>
186-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:128:13-130:29
187                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
187-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:129:17-98
187-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:129:25-95
188            </intent-filter>
189        </receiver>
190        <receiver
190-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:132:9-142:20
191            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
191-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:133:13-78
192            android:directBootAware="false"
192-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:134:13-44
193            android:enabled="true"
193-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:135:13-35
194            android:exported="true"
194-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:136:13-36
195            android:permission="android.permission.DUMP" >
195-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:137:13-57
196            <intent-filter>
196-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:139:13-141:29
197                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
197-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:140:17-88
197-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:140:25-85
198            </intent-filter>
199        </receiver>
200
201        <service
201-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\891ad734ff703f843ef3afba41c84481\transformed\room-runtime-2.5.0\AndroidManifest.xml:24:9-28:63
202            android:name="androidx.room.MultiInstanceInvalidationService"
202-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\891ad734ff703f843ef3afba41c84481\transformed\room-runtime-2.5.0\AndroidManifest.xml:25:13-74
203            android:directBootAware="true"
203-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\891ad734ff703f843ef3afba41c84481\transformed\room-runtime-2.5.0\AndroidManifest.xml:26:13-43
204            android:exported="false" />
204-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\891ad734ff703f843ef3afba41c84481\transformed\room-runtime-2.5.0\AndroidManifest.xml:27:13-37
205
206        <receiver
206-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\855f345d0f57b580ddb9a53f14817127\transformed\profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
207            android:name="androidx.profileinstaller.ProfileInstallReceiver"
207-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\855f345d0f57b580ddb9a53f14817127\transformed\profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
208            android:directBootAware="false"
208-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\855f345d0f57b580ddb9a53f14817127\transformed\profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
209            android:enabled="true"
209-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\855f345d0f57b580ddb9a53f14817127\transformed\profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
210            android:exported="true"
210-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\855f345d0f57b580ddb9a53f14817127\transformed\profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
211            android:permission="android.permission.DUMP" >
211-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\855f345d0f57b580ddb9a53f14817127\transformed\profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
212            <intent-filter>
212-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\855f345d0f57b580ddb9a53f14817127\transformed\profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
213                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
213-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\855f345d0f57b580ddb9a53f14817127\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
213-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\855f345d0f57b580ddb9a53f14817127\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
214            </intent-filter>
215            <intent-filter>
215-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\855f345d0f57b580ddb9a53f14817127\transformed\profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
216                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
216-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\855f345d0f57b580ddb9a53f14817127\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
216-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\855f345d0f57b580ddb9a53f14817127\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
217            </intent-filter>
218            <intent-filter>
218-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\855f345d0f57b580ddb9a53f14817127\transformed\profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
219                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
219-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\855f345d0f57b580ddb9a53f14817127\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
219-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\855f345d0f57b580ddb9a53f14817127\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
220            </intent-filter>
221            <intent-filter>
221-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\855f345d0f57b580ddb9a53f14817127\transformed\profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
222                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
222-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\855f345d0f57b580ddb9a53f14817127\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
222-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\855f345d0f57b580ddb9a53f14817127\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
223            </intent-filter>
224        </receiver>
225    </application>
226
227</manifest>
