package com.enhanced.videowidget.utils;

import java.lang.System;

/**
 * Utility class for extracting frames from video files
 */
@kotlin.Metadata(mv = {1, 8, 0}, k = 1, d1 = {"\u0000B\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\t\n\u0000\n\u0002\u0010\b\n\u0002\b\u0003\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0010\u000b\n\u0002\b\u0005\u0018\u0000 \u001b2\u00020\u0001:\u0003\u001b\u001c\u001dB\u0005\u00a2\u0006\u0002\u0010\u0002J7\u0010\u0003\u001a\u0004\u0018\u00010\u00042\u0006\u0010\u0005\u001a\u00020\u00062\u0006\u0010\u0007\u001a\u00020\b2\b\b\u0002\u0010\t\u001a\u00020\n2\b\b\u0002\u0010\u000b\u001a\u00020\nH\u0086@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010\fJE\u0010\r\u001a\b\u0012\u0004\u0012\u00020\u000f0\u000e2\u0006\u0010\u0005\u001a\u00020\u00062\u0006\u0010\u0010\u001a\u00020\u00062\b\b\u0002\u0010\u0011\u001a\u00020\n2\b\b\u0002\u0010\t\u001a\u00020\n2\b\b\u0002\u0010\u000b\u001a\u00020\nH\u0086@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010\u0012J\u001b\u0010\u0013\u001a\u0004\u0018\u00010\u00142\u0006\u0010\u0005\u001a\u00020\u0006H\u0086@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010\u0015J \u0010\u0016\u001a\u00020\u00042\u0006\u0010\u0017\u001a\u00020\u00042\u0006\u0010\t\u001a\u00020\n2\u0006\u0010\u000b\u001a\u00020\nH\u0002J\u0018\u0010\u0018\u001a\u00020\u00192\u0006\u0010\u0017\u001a\u00020\u00042\u0006\u0010\u001a\u001a\u00020\u0006H\u0002\u0082\u0002\u0004\n\u0002\b\u0019\u00a8\u0006\u001e"}, d2 = {"Lcom/enhanced/videowidget/utils/VideoFrameExtractor;", "", "()V", "extractFrameAtTime", "Landroid/graphics/Bitmap;", "videoPath", "", "timestampMs", "", "targetWidth", "", "targetHeight", "(Ljava/lang/String;JIILkotlin/coroutines/Continuation;)Ljava/lang/Object;", "extractFrames", "", "Lcom/enhanced/videowidget/utils/VideoFrameExtractor$FrameInfo;", "outputDirectory", "maxFrames", "(Ljava/lang/String;Ljava/lang/String;IIILkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getVideoMetadata", "Lcom/enhanced/videowidget/utils/VideoFrameExtractor$VideoMetadata;", "(Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "resizeBitmap", "bitmap", "saveFrameToFile", "", "filePath", "Companion", "FrameInfo", "VideoMetadata", "app_debug"})
public final class VideoFrameExtractor {
    @org.jetbrains.annotations.NotNull
    public static final com.enhanced.videowidget.utils.VideoFrameExtractor.Companion Companion = null;
    private static final int DEFAULT_MAX_FRAMES = 100;
    private static final int DEFAULT_TARGET_SIZE = 300;
    
    public VideoFrameExtractor() {
        super();
    }
    
    /**
     * Extract frames from a video file
     */
    @org.jetbrains.annotations.Nullable
    public final java.lang.Object extractFrames(@org.jetbrains.annotations.NotNull
    java.lang.String videoPath, @org.jetbrains.annotations.NotNull
    java.lang.String outputDirectory, int maxFrames, int targetWidth, int targetHeight, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.util.List<com.enhanced.videowidget.utils.VideoFrameExtractor.FrameInfo>> continuation) {
        return null;
    }
    
    /**
     * Extract a single frame at a specific timestamp
     */
    @org.jetbrains.annotations.Nullable
    public final java.lang.Object extractFrameAtTime(@org.jetbrains.annotations.NotNull
    java.lang.String videoPath, long timestampMs, int targetWidth, int targetHeight, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super android.graphics.Bitmap> continuation) {
        return null;
    }
    
    /**
     * Get video metadata
     */
    @org.jetbrains.annotations.Nullable
    public final java.lang.Object getVideoMetadata(@org.jetbrains.annotations.NotNull
    java.lang.String videoPath, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super com.enhanced.videowidget.utils.VideoFrameExtractor.VideoMetadata> continuation) {
        return null;
    }
    
    private final android.graphics.Bitmap resizeBitmap(android.graphics.Bitmap bitmap, int targetWidth, int targetHeight) {
        return null;
    }
    
    private final boolean saveFrameToFile(android.graphics.Bitmap bitmap, java.lang.String filePath) {
        return false;
    }
    
    @kotlin.Metadata(mv = {1, 8, 0}, k = 1, d1 = {"\u0000&\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0010\t\n\u0000\n\u0002\u0010\u000e\n\u0002\b\f\n\u0002\u0010\u000b\n\u0002\b\u0004\b\u0086\b\u0018\u00002\u00020\u0001B\u001d\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0007\u00a2\u0006\u0002\u0010\bJ\t\u0010\u000f\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u0010\u001a\u00020\u0005H\u00c6\u0003J\t\u0010\u0011\u001a\u00020\u0007H\u00c6\u0003J\'\u0010\u0012\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u0007H\u00c6\u0001J\u0013\u0010\u0013\u001a\u00020\u00142\b\u0010\u0015\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u0016\u001a\u00020\u0003H\u00d6\u0001J\t\u0010\u0017\u001a\u00020\u0007H\u00d6\u0001R\u0011\u0010\u0006\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b\t\u0010\nR\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000b\u0010\fR\u0011\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\r\u0010\u000e\u00a8\u0006\u0018"}, d2 = {"Lcom/enhanced/videowidget/utils/VideoFrameExtractor$FrameInfo;", "", "frameIndex", "", "timestamp", "", "filePath", "", "(IJLjava/lang/String;)V", "getFilePath", "()Ljava/lang/String;", "getFrameIndex", "()I", "getTimestamp", "()J", "component1", "component2", "component3", "copy", "equals", "", "other", "hashCode", "toString", "app_debug"})
    public static final class FrameInfo {
        private final int frameIndex = 0;
        private final long timestamp = 0L;
        @org.jetbrains.annotations.NotNull
        private final java.lang.String filePath = null;
        
        @org.jetbrains.annotations.NotNull
        public final com.enhanced.videowidget.utils.VideoFrameExtractor.FrameInfo copy(int frameIndex, long timestamp, @org.jetbrains.annotations.NotNull
        java.lang.String filePath) {
            return null;
        }
        
        @java.lang.Override
        public boolean equals(@org.jetbrains.annotations.Nullable
        java.lang.Object other) {
            return false;
        }
        
        @java.lang.Override
        public int hashCode() {
            return 0;
        }
        
        @org.jetbrains.annotations.NotNull
        @java.lang.Override
        public java.lang.String toString() {
            return null;
        }
        
        public FrameInfo(int frameIndex, long timestamp, @org.jetbrains.annotations.NotNull
        java.lang.String filePath) {
            super();
        }
        
        public final int component1() {
            return 0;
        }
        
        public final int getFrameIndex() {
            return 0;
        }
        
        public final long component2() {
            return 0L;
        }
        
        public final long getTimestamp() {
            return 0L;
        }
        
        @org.jetbrains.annotations.NotNull
        public final java.lang.String component3() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull
        public final java.lang.String getFilePath() {
            return null;
        }
    }
    
    @kotlin.Metadata(mv = {1, 8, 0}, k = 1, d1 = {"\u0000.\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\t\n\u0000\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0010\u0007\n\u0002\b\u000e\n\u0002\u0010\u000b\n\u0002\b\u0003\n\u0002\u0010\u000e\n\u0000\b\u0086\b\u0018\u00002\u00020\u0001B%\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0005\u0012\u0006\u0010\u0007\u001a\u00020\b\u00a2\u0006\u0002\u0010\tJ\t\u0010\u0011\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u0012\u001a\u00020\u0005H\u00c6\u0003J\t\u0010\u0013\u001a\u00020\u0005H\u00c6\u0003J\t\u0010\u0014\u001a\u00020\bH\u00c6\u0003J1\u0010\u0015\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u00052\b\b\u0002\u0010\u0007\u001a\u00020\bH\u00c6\u0001J\u0013\u0010\u0016\u001a\u00020\u00172\b\u0010\u0018\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u0019\u001a\u00020\u0005H\u00d6\u0001J\t\u0010\u001a\u001a\u00020\u001bH\u00d6\u0001R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\n\u0010\u000bR\u0011\u0010\u0007\u001a\u00020\b\u00a2\u0006\b\n\u0000\u001a\u0004\b\f\u0010\rR\u0011\u0010\u0006\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000e\u0010\u000fR\u0011\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0010\u0010\u000f\u00a8\u0006\u001c"}, d2 = {"Lcom/enhanced/videowidget/utils/VideoFrameExtractor$VideoMetadata;", "", "duration", "", "width", "", "height", "frameRate", "", "(JIIF)V", "getDuration", "()J", "getFrameRate", "()F", "getHeight", "()I", "getWidth", "component1", "component2", "component3", "component4", "copy", "equals", "", "other", "hashCode", "toString", "", "app_debug"})
    public static final class VideoMetadata {
        private final long duration = 0L;
        private final int width = 0;
        private final int height = 0;
        private final float frameRate = 0.0F;
        
        @org.jetbrains.annotations.NotNull
        public final com.enhanced.videowidget.utils.VideoFrameExtractor.VideoMetadata copy(long duration, int width, int height, float frameRate) {
            return null;
        }
        
        @java.lang.Override
        public boolean equals(@org.jetbrains.annotations.Nullable
        java.lang.Object other) {
            return false;
        }
        
        @java.lang.Override
        public int hashCode() {
            return 0;
        }
        
        @org.jetbrains.annotations.NotNull
        @java.lang.Override
        public java.lang.String toString() {
            return null;
        }
        
        public VideoMetadata(long duration, int width, int height, float frameRate) {
            super();
        }
        
        public final long component1() {
            return 0L;
        }
        
        public final long getDuration() {
            return 0L;
        }
        
        public final int component2() {
            return 0;
        }
        
        public final int getWidth() {
            return 0;
        }
        
        public final int component3() {
            return 0;
        }
        
        public final int getHeight() {
            return 0;
        }
        
        public final float component4() {
            return 0.0F;
        }
        
        public final float getFrameRate() {
            return 0.0F;
        }
    }
    
    @kotlin.Metadata(mv = {1, 8, 0}, k = 1, d1 = {"\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0006"}, d2 = {"Lcom/enhanced/videowidget/utils/VideoFrameExtractor$Companion;", "", "()V", "DEFAULT_MAX_FRAMES", "", "DEFAULT_TARGET_SIZE", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
    }
}