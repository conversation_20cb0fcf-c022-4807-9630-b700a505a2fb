package com.enhanced.videowidget.worker

import android.appwidget.AppWidgetManager
import android.content.Context
import android.graphics.Bitmap
import android.util.Log
import androidx.work.CoroutineWorker
import androidx.work.Data
import androidx.work.WorkerParameters
import com.enhanced.videowidget.data.model.Widget
import com.enhanced.videowidget.data.repository.WidgetRepository
import com.enhanced.videowidget.utils.VideoFrameExtractor
import com.enhanced.videowidget.widget.EnhancedVideoWidget
import kotlinx.coroutines.delay
import kotlinx.coroutines.isActive
import kotlin.coroutines.coroutineContext
import com.enhanced.videowidget.utils.AppLogger

/**
 * Background worker for processing video frames and updating widgets
 */
class WidgetUpdateWorker(
    context: Context,
    params: WorkerParameters
) : CoroutineWorker(context, params) {
    
    companion object {
        private const val TAG = "WidgetUpdateWorker"
        private const val DEFAULT_FRAME_DELAY = 33L // ~30 FPS
    }
    
    private val repository = WidgetRepository.getInstance()
    private val appWidgetManager = AppWidgetManager.getInstance(applicationContext)
    
    override suspend fun doWork(): Result {
        val widgetId = inputData.getInt(EnhancedVideoWidget.EXTRA_WIDGET_ID, -1)
        if (widgetId == -1) {
            AppLogger.e(AppLogger.TAG_WORKER, "Invalid widget ID received")
            return Result.failure()
        }

        AppLogger.logWorkerStart(widgetId)

        repository.initialize(applicationContext)
        AppLogger.d(AppLogger.TAG_WORKER, "Repository initialized for widget $widgetId")

        return try {
            processWidgetFrames(widgetId)
            AppLogger.logWorkerEnd(widgetId, true)
            Result.success()
        } catch (e: Exception) {
            AppLogger.logError(AppLogger.TAG_WORKER, "processWidgetFrames for widget $widgetId", e)
            AppLogger.logWorkerEnd(widgetId, false)
            Result.failure()
        }
    }
    
    private suspend fun processWidgetFrames(widgetId: Int) {
        AppLogger.d(AppLogger.TAG_WORKER, "=== PROCESSING WIDGET FRAMES for Widget $widgetId ===")

        var widget = repository.getWidget(widgetId)
        if (widget == null) {
            AppLogger.w(AppLogger.TAG_WORKER, "Widget $widgetId not found in repository")
            return
        }

        AppLogger.d(AppLogger.TAG_WORKER, "Widget found - Video: ${widget.videoPath}, Frames: ${widget.frameFiles.size}")
        
        // If widget is not playing or is paused, don't process frames
        if (!widget.shouldAnimate()) {
            AppLogger.d(AppLogger.TAG_WORKER, "Widget $widgetId is not animating, stopping work")
            return
        }
        
        // Check if we need to extract frames first
        if (widget.totalFrames == 0 && !widget.videoPath.isNullOrEmpty()) {
            extractVideoFrames(widget)
            widget = repository.getWidget(widgetId) ?: return
        }
        
        // Start frame animation loop
        animateFrames(widget)
    }
    
    private suspend fun extractVideoFrames(widget: Widget) {
        AppLogger.i(AppLogger.TAG_WORKER, "=== EXTRACTING VIDEO FRAMES for Widget ${widget.id} ===")

        if (widget.videoPath.isNullOrEmpty()) {
            AppLogger.e(AppLogger.TAG_WORKER, "No video path specified for widget ${widget.id}")
            return
        }

        AppLogger.d(AppLogger.TAG_WORKER, "Video path: ${widget.videoPath}")
        
        val frameExtractor = VideoFrameExtractor()
        val frameDirectory = repository.getFrameDirectory(widget.id) ?: return
        
        try {
            val extractedFrames = frameExtractor.extractFrames(
                videoPath = widget.videoPath,
                outputDirectory = frameDirectory,
                maxFrames = 100, // Limit frames to prevent excessive storage usage
                targetWidth = 300,
                targetHeight = 300
            )
            
            if (extractedFrames.isNotEmpty()) {
                // Load first frame as preview
                val firstFrame = repository.loadFrameBitmap(widget.id, 0)
                
                // Update widget with frame information
                val updatedWidget = widget.copy(
                    totalFrames = extractedFrames.size,
                    firstFrame = firstFrame,
                    framesDirectory = frameDirectory
                )
                
                repository.addOrUpdateWidget(updatedWidget)
                
                // Update widget UI with first frame
                EnhancedVideoWidget.updateAppWidget(
                    applicationContext,
                    appWidgetManager,
                    widget.id,
                    updatedWidget,
                    firstFrame
                )
                
                AppLogger.i(AppLogger.TAG_WORKER, "Successfully extracted ${extractedFrames.size} frames for widget ${widget.id}")
            } else {
                AppLogger.w(AppLogger.TAG_WORKER, "No frames extracted for widget ${widget.id}")
            }
        } catch (e: Exception) {
            AppLogger.logError(AppLogger.TAG_WORKER, "extracting frames for widget ${widget.id}", e)
        }
    }
    
    private suspend fun animateFrames(initialWidget: Widget) {
        var widget = initialWidget
        val frameDelay = calculateFrameDelay(widget.framerate)
        
        Log.d(TAG, "Starting animation for widget ${widget.id} with ${widget.totalFrames} frames")
        
        while (coroutineContext.isActive) {
            // Check if widget still exists and should be animating
            widget = repository.getWidget(widget.id) ?: break
            
            if (!widget.shouldAnimate()) {
                Log.d(TAG, "Widget ${widget.id} stopped animating")
                break
            }
            
            // Load current frame
            val frameBitmap = repository.loadFrameBitmap(widget.id, widget.currentFrameIndex)
            
            if (frameBitmap != null) {
                // Update widget UI with current frame
                EnhancedVideoWidget.updateAppWidget(
                    applicationContext,
                    appWidgetManager,
                    widget.id,
                    widget,
                    frameBitmap
                )
                
                // Move to next frame
                val nextFrameIndex = widget.getNextFrameIndex()
                repository.updateWidgetFrameIndex(widget.id, nextFrameIndex)
                widget = widget.withFrameIndex(nextFrameIndex)
                
                Log.v(TAG, "Updated widget ${widget.id} to frame ${widget.currentFrameIndex}")
            } else {
                Log.w(TAG, "Failed to load frame ${widget.currentFrameIndex} for widget ${widget.id}")
                
                // If we can't load the frame, try to re-extract frames
                if (!widget.videoPath.isNullOrEmpty()) {
                    extractVideoFrames(widget)
                    widget = repository.getWidget(widget.id) ?: break
                } else {
                    break
                }
            }
            
            // Wait for next frame
            delay(frameDelay)
        }
        
        Log.d(TAG, "Animation stopped for widget ${widget.id}")
    }
    
    private fun calculateFrameDelay(framerate: Float): Long {
        return if (framerate > 0) {
            (1000 / framerate).toLong()
        } else {
            DEFAULT_FRAME_DELAY
        }
    }
}
