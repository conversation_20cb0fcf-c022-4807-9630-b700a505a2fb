package com.enhanced.videowidget.worker

import android.appwidget.AppWidgetManager
import android.content.Context
import android.graphics.Bitmap
import android.util.Log
import androidx.work.CoroutineWorker
import androidx.work.Data
import androidx.work.WorkerParameters
import com.enhanced.videowidget.data.repository.WidgetRepository
import com.enhanced.videowidget.utils.VideoFrameExtractor
import com.enhanced.videowidget.widget.EnhancedVideoWidget
import kotlinx.coroutines.delay
import kotlinx.coroutines.isActive

/**
 * Background worker for processing video frames and updating widgets
 */
class WidgetUpdateWorker(
    context: Context,
    params: WorkerParameters
) : CoroutineWorker(context, params) {
    
    companion object {
        private const val TAG = "WidgetUpdateWorker"
        private const val DEFAULT_FRAME_DELAY = 33L // ~30 FPS
    }
    
    private val repository = WidgetRepository.getInstance()
    private val appWidgetManager = AppWidgetManager.getInstance(applicationContext)
    
    override suspend fun doWork(): Result {
        val widgetId = inputData.getInt(EnhancedVideoWidget.EXTRA_WIDGET_ID, -1)
        if (widgetId == -1) {
            Log.e(TAG, "Invalid widget ID")
            return Result.failure()
        }
        
        Log.d(TAG, "Starting widget update work for widget $widgetId")
        
        repository.initialize(applicationContext)
        
        return try {
            processWidgetFrames(widgetId)
            Result.success()
        } catch (e: Exception) {
            Log.e(TAG, "Error processing widget frames", e)
            Result.failure()
        }
    }
    
    private suspend fun processWidgetFrames(widgetId: Int) {
        var widget = repository.getWidget(widgetId)
        if (widget == null) {
            Log.w(TAG, "Widget $widgetId not found in repository")
            return
        }
        
        // If widget is not playing or is paused, don't process frames
        if (!widget.shouldAnimate()) {
            Log.d(TAG, "Widget $widgetId is not animating, stopping work")
            return
        }
        
        // Check if we need to extract frames first
        if (widget.totalFrames == 0 && !widget.videoPath.isNullOrEmpty()) {
            extractVideoFrames(widget)
            widget = repository.getWidget(widgetId) ?: return
        }
        
        // Start frame animation loop
        animateFrames(widget)
    }
    
    private suspend fun extractVideoFrames(widget: Widget) {
        Log.d(TAG, "Extracting frames for widget ${widget.id}")
        
        if (widget.videoPath.isNullOrEmpty()) {
            Log.e(TAG, "No video path specified for widget ${widget.id}")
            return
        }
        
        val frameExtractor = VideoFrameExtractor()
        val frameDirectory = repository.getFrameDirectory(widget.id) ?: return
        
        try {
            val extractedFrames = frameExtractor.extractFrames(
                videoPath = widget.videoPath,
                outputDirectory = frameDirectory,
                maxFrames = 100, // Limit frames to prevent excessive storage usage
                targetWidth = 300,
                targetHeight = 300
            )
            
            if (extractedFrames.isNotEmpty()) {
                // Load first frame as preview
                val firstFrame = repository.loadFrameBitmap(widget.id, 0)
                
                // Update widget with frame information
                val updatedWidget = widget.copy(
                    totalFrames = extractedFrames.size,
                    firstFrame = firstFrame,
                    framesDirectory = frameDirectory
                )
                
                repository.addOrUpdateWidget(updatedWidget)
                
                // Update widget UI with first frame
                EnhancedVideoWidget.updateAppWidget(
                    applicationContext,
                    appWidgetManager,
                    widget.id,
                    updatedWidget,
                    firstFrame
                )
                
                Log.d(TAG, "Extracted ${extractedFrames.size} frames for widget ${widget.id}")
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error extracting frames for widget ${widget.id}", e)
        }
    }
    
    private suspend fun animateFrames(initialWidget: Widget) {
        var widget = initialWidget
        val frameDelay = calculateFrameDelay(widget.framerate)
        
        Log.d(TAG, "Starting animation for widget ${widget.id} with ${widget.totalFrames} frames")
        
        while (isActive) {
            // Check if widget still exists and should be animating
            widget = repository.getWidget(widget.id) ?: break
            
            if (!widget.shouldAnimate()) {
                Log.d(TAG, "Widget ${widget.id} stopped animating")
                break
            }
            
            // Load current frame
            val frameBitmap = repository.loadFrameBitmap(widget.id, widget.currentFrameIndex)
            
            if (frameBitmap != null) {
                // Update widget UI with current frame
                EnhancedVideoWidget.updateAppWidget(
                    applicationContext,
                    appWidgetManager,
                    widget.id,
                    widget,
                    frameBitmap
                )
                
                // Move to next frame
                val nextFrameIndex = widget.getNextFrameIndex()
                repository.updateWidgetFrameIndex(widget.id, nextFrameIndex)
                widget = widget.withFrameIndex(nextFrameIndex)
                
                Log.v(TAG, "Updated widget ${widget.id} to frame ${widget.currentFrameIndex}")
            } else {
                Log.w(TAG, "Failed to load frame ${widget.currentFrameIndex} for widget ${widget.id}")
                
                // If we can't load the frame, try to re-extract frames
                if (!widget.videoPath.isNullOrEmpty()) {
                    extractVideoFrames(widget)
                    widget = repository.getWidget(widget.id) ?: break
                } else {
                    break
                }
            }
            
            // Wait for next frame
            delay(frameDelay)
        }
        
        Log.d(TAG, "Animation stopped for widget ${widget.id}")
    }
    
    private fun calculateFrameDelay(framerate: Float): Long {
        return if (framerate > 0) {
            (1000 / framerate).toLong()
        } else {
            DEFAULT_FRAME_DELAY
        }
    }
}
