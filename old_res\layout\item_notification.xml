<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@color/backgroundSecondary"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="8dp"
    app:cardCornerRadius="16dp"
    app:cardElevation="0dp">
    <androidx.constraintlayout.widget.ConstraintLayout
        android:background="@color/backgroundSecondary"
        android:padding="16dp"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">
        <TextView
            android:textSize="22sp"
            android:textStyle="bold"
            android:textColor="@color/textColor"
            android:id="@+id/tvNotificationTitle"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:text="Notification Title"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"/>
        <TextView
            android:textSize="14sp"
            android:textColor="@android:color/darker_gray"
            android:id="@+id/tvNotificationDescription"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:text="This is a notification description."
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tvNotificationTitle"/>
        <com.google.android.material.button.MaterialButton
            android:textColor="@android:color/white"
            android:id="@+id/btnNotificationAction"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:text="Update"
            app:backgroundTint="@color/blue"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tvNotificationDescription"/>
    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.cardview.widget.CardView>
