{"logs": [{"outputFile": "com.enhanced.videowidget.app-mergeDebugResources-45:/values-te/values-te.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8c486cec99d4fccb7893779ad5d22534\\transformed\\material-1.9.0\\res\\values-te\\values-te.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,276,365,455,541,639,726,830,946,1037,1103,1197,1264,1326,1419,1483,1551,1614,1688,1753,1807,1928,1985,2047,2101,2180,2308,2396,2488,2633,2713,2795,2883,2941,2993,3059,3134,3212,3302,3375,3451,3532,3601,3706,3783,3874,3967,4041,4118,4210,4265,4331,4415,4501,4564,4629,4693,4803,4915,5014,5133,5197,5253", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "endColumns": "12,88,89,85,97,86,103,115,90,65,93,66,61,92,63,67,62,73,64,53,120,56,61,53,78,127,87,91,144,79,81,87,57,51,65,74,77,89,72,75,80,68,104,76,90,92,73,76,91,54,65,83,85,62,64,63,109,111,98,118,63,55,82", "endOffsets": "271,360,450,536,634,721,825,941,1032,1098,1192,1259,1321,1414,1478,1546,1609,1683,1748,1802,1923,1980,2042,2096,2175,2303,2391,2483,2628,2708,2790,2878,2936,2988,3054,3129,3207,3297,3370,3446,3527,3596,3701,3778,3869,3962,4036,4113,4205,4260,4326,4410,4496,4559,4624,4688,4798,4910,5009,5128,5192,5248,5331"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3080,3169,3259,3345,3443,4280,4384,4500,4591,4657,4751,4818,4880,4973,5037,5105,5168,5242,5307,5361,5482,5539,5601,5655,5734,5862,5950,6042,6187,6267,6349,6437,6495,6547,6613,6688,6766,6856,6929,7005,7086,7155,7260,7337,7428,7521,7595,7672,7764,7819,7885,7969,8055,8118,8183,8247,8357,8469,8568,8687,8751,8807", "endLines": "5,33,34,35,36,37,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101", "endColumns": "12,88,89,85,97,86,103,115,90,65,93,66,61,92,63,67,62,73,64,53,120,56,61,53,78,127,87,91,144,79,81,87,57,51,65,74,77,89,72,75,80,68,104,76,90,92,73,76,91,54,65,83,85,62,64,63,109,111,98,118,63,55,82", "endOffsets": "321,3164,3254,3340,3438,3525,4379,4495,4586,4652,4746,4813,4875,4968,5032,5100,5163,5237,5302,5356,5477,5534,5596,5650,5729,5857,5945,6037,6182,6262,6344,6432,6490,6542,6608,6683,6761,6851,6924,7000,7081,7150,7255,7332,7423,7516,7590,7667,7759,7814,7880,7964,8050,8113,8178,8242,8352,8464,8563,8682,8746,8802,8885"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\6fe3e64a80094b4316c7006f0bcdcf47\\transformed\\core-1.10.1\\res\\values-te\\values-te.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,157,265,367,468,574,681,805", "endColumns": "101,107,101,100,105,106,123,100", "endOffsets": "152,260,362,463,569,676,800,901"}, "to": {"startLines": "38,39,40,41,42,43,44,103", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3530,3632,3740,3842,3943,4049,4156,8973", "endColumns": "101,107,101,100,105,106,123,100", "endOffsets": "3627,3735,3837,3938,4044,4151,4275,9069"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\3c82f0ccdbe9412677623492bdd8999d\\transformed\\appcompat-1.6.1\\res\\values-te\\values-te.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,222,334,447,537,642,761,839,915,1006,1099,1194,1288,1388,1481,1576,1671,1762,1853,1942,2056,2160,2259,2374,2479,2594,2756,2859", "endColumns": "116,111,112,89,104,118,77,75,90,92,94,93,99,92,94,94,90,90,88,113,103,98,114,104,114,161,102,82", "endOffsets": "217,329,442,532,637,756,834,910,1001,1094,1189,1283,1383,1476,1571,1666,1757,1848,1937,2051,2155,2254,2369,2474,2589,2751,2854,2937"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,102", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "326,443,555,668,758,863,982,1060,1136,1227,1320,1415,1509,1609,1702,1797,1892,1983,2074,2163,2277,2381,2480,2595,2700,2815,2977,8890", "endColumns": "116,111,112,89,104,118,77,75,90,92,94,93,99,92,94,94,90,90,88,113,103,98,114,104,114,161,102,82", "endOffsets": "438,550,663,753,858,977,1055,1131,1222,1315,1410,1504,1604,1697,1792,1887,1978,2069,2158,2272,2376,2475,2590,2695,2810,2972,3075,8968"}}]}]}