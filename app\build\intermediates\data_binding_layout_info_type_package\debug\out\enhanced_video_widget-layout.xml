<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="enhanced_video_widget" modulePackage="com.enhanced.videowidget" filePath="app\src\main\res\layout\enhanced_video_widget.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.FrameLayout" rootNodeViewId="@+id/widget_container"><Targets><Target id="@+id/widget_container" tag="layout/enhanced_video_widget_0" view="FrameLayout"><Expressions/><location startLine="1" startOffset="0" endLine="111" endOffset="13"/></Target><Target id="@+id/imageVideoView" view="ImageView"><Expressions/><location startLine="10" startOffset="4" endLine="17" endOffset="49"/></Target><Target id="@+id/progressBar" view="ProgressBar"><Expressions/><location startLine="20" startOffset="4" endLine="27" endOffset="53"/></Target><Target id="@+id/controlOverlay" view="LinearLayout"><Expressions/><location startLine="30" startOffset="4" endLine="94" endOffset="18"/></Target><Target id="@+id/playPauseButton" view="ImageButton"><Expressions/><location startLine="41" startOffset="8" endLine="50" endOffset="35"/></Target><Target id="@+id/widgetTitle" view="TextView"><Expressions/><location startLine="59" startOffset="12" endLine="68" endOffset="41"/></Target><Target id="@+id/widgetStatus" view="TextView"><Expressions/><location startLine="70" startOffset="12" endLine="79" endOffset="41"/></Target><Target id="@+id/settingsButton" view="ImageButton"><Expressions/><location startLine="84" startOffset="8" endLine="92" endOffset="35"/></Target><Target id="@+id/frameCounter" view="TextView"><Expressions/><location startLine="97" startOffset="4" endLine="109" endOffset="36"/></Target></Targets></Layout>