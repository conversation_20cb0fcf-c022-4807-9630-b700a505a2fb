<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:clipChildren="true"
    android:clipToPadding="true"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">
    <androidx.cardview.widget.CardView
        android:id="@+id/imageViewWidgetCard"
        android:clipChildren="true"
        android:clipToPadding="true"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_margin="8dp"
        android:backgroundTint="#00000000"
        app:cardCornerRadius="24dp"
        app:cardElevation="0dp"
        app:cardUseCompatPadding="false"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintDimensionRatio="1:1"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">
        <FrameLayout
            android:clipChildren="true"
            android:clipToPadding="true"
            android:layout_width="match_parent"
            android:layout_height="match_parent">
            <com.google.android.exoplayer2.ui.PlayerView
                android:id="@+id/playerView"
                android:background="@android:color/transparent"
                android:visibility="gone"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                app:resize_mode="zoom"
                app:shutter_background_color="@android:color/transparent"
                app:surface_type="texture_view"
                app:use_controller="false"/>
            <ImageView
                android:id="@+id/imageViewWidget"
                android:background="@android:color/transparent"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:scaleType="centerCrop"/>
        </FrameLayout>
    </androidx.cardview.widget.CardView>
</androidx.constraintlayout.widget.ConstraintLayout>
