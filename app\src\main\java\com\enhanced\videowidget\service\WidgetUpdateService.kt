package com.enhanced.videowidget.service

import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.Service
import android.content.Context
import android.content.Intent
import android.os.Build
import android.os.IBinder
import android.util.Log
import androidx.core.app.NotificationCompat
import com.enhanced.videowidget.R

/**
 * Foreground service for managing widget updates
 */
class WidgetUpdateService : Service() {
    
    companion object {
        private const val TAG = "WidgetUpdateService"
        private const val NOTIFICATION_ID = 1001
        private const val CHANNEL_ID = "widget_update_channel"
        
        const val ACTION_START_UPDATES = "com.enhanced.videowidget.ACTION_START_UPDATES"
        const val ACTION_STOP_UPDATES = "com.enhanced.videowidget.ACTION_STOP_UPDATES"
    }
    
    override fun onCreate() {
        super.onCreate()
        Log.d(TAG, "WidgetUpdateService created")
        createNotificationChannel()
    }
    
    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        Log.d(TAG, "onStartCommand: ${intent?.action}")
        
        when (intent?.action) {
            ACTION_START_UPDATES -> {
                startForeground(NOTIFICATION_ID, createNotification())
                // Service logic would be handled by WorkManager
            }
            ACTION_STOP_UPDATES -> {
                stopSelf()
            }
        }
        
        return START_STICKY
    }
    
    override fun onBind(intent: Intent?): IBinder? {
        return null
    }
    
    override fun onDestroy() {
        super.onDestroy()
        Log.d(TAG, "WidgetUpdateService destroyed")
    }
    
    private fun createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannel(
                CHANNEL_ID,
                "Widget Updates",
                NotificationManager.IMPORTANCE_LOW
            ).apply {
                description = "Manages video widget updates"
                setShowBadge(false)
            }
            
            val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            notificationManager.createNotificationChannel(channel)
        }
    }
    
    private fun createNotification(): Notification {
        return NotificationCompat.Builder(this, CHANNEL_ID)
            .setContentTitle("Enhanced Video Widgets")
            .setContentText("Managing video widget updates")
            .setSmallIcon(R.drawable.ic_video_widget_placeholder)
            .setOngoing(true)
            .setShowWhen(false)
            .build()
    }
}
