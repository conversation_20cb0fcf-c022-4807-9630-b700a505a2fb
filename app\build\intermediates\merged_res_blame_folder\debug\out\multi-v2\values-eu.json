{"logs": [{"outputFile": "com.enhanced.videowidget.app-mergeDebugResources-45:/values-eu/values-eu.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\3c82f0ccdbe9412677623492bdd8999d\\transformed\\appcompat-1.6.1\\res\\values-eu\\values-eu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,214,312,422,508,614,738,824,905,997,1091,1187,1281,1382,1476,1572,1669,1761,1854,1936,2045,2154,2253,2362,2469,2580,2751,2850", "endColumns": "108,97,109,85,105,123,85,80,91,93,95,93,100,93,95,96,91,92,81,108,108,98,108,106,110,170,98,82", "endOffsets": "209,307,417,503,609,733,819,900,992,1086,1182,1276,1377,1471,1567,1664,1756,1849,1931,2040,2149,2248,2357,2464,2575,2746,2845,2928"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,102", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "325,434,532,642,728,834,958,1044,1125,1217,1311,1407,1501,1602,1696,1792,1889,1981,2074,2156,2265,2374,2473,2582,2689,2800,2971,8809", "endColumns": "108,97,109,85,105,123,85,80,91,93,95,93,100,93,95,96,91,92,81,108,108,98,108,106,110,170,98,82", "endOffsets": "429,527,637,723,829,953,1039,1120,1212,1306,1402,1496,1597,1691,1787,1884,1976,2069,2151,2260,2369,2468,2577,2684,2795,2966,3065,8887"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8c486cec99d4fccb7893779ad5d22534\\transformed\\material-1.9.0\\res\\values-eu\\values-eu.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,275,382,487,567,674,774,872,987,1070,1137,1236,1304,1365,1453,1516,1582,1646,1717,1780,1834,1943,2002,2065,2119,2193,2318,2408,2488,2633,2716,2798,2889,2941,2994,3060,3131,3211,3297,3375,3453,3526,3601,3688,3775,3866,3959,4031,4107,4199,4250,4316,4400,4486,4548,4612,4675,4782,4887,4983,5089,5145,5202", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "endColumns": "12,106,104,79,106,99,97,114,82,66,98,67,60,87,62,65,63,70,62,53,108,58,62,53,73,124,89,79,144,82,81,90,51,52,65,70,79,85,77,77,72,74,86,86,90,92,71,75,91,50,65,83,85,61,63,62,106,104,95,105,55,56,82", "endOffsets": "270,377,482,562,669,769,867,982,1065,1132,1231,1299,1360,1448,1511,1577,1641,1712,1775,1829,1938,1997,2060,2114,2188,2313,2403,2483,2628,2711,2793,2884,2936,2989,3055,3126,3206,3292,3370,3448,3521,3596,3683,3770,3861,3954,4026,4102,4194,4245,4311,4395,4481,4543,4607,4670,4777,4882,4978,5084,5140,5197,5280"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3070,3177,3282,3362,3469,4298,4396,4511,4594,4661,4760,4828,4889,4977,5040,5106,5170,5241,5304,5358,5467,5526,5589,5643,5717,5842,5932,6012,6157,6240,6322,6413,6465,6518,6584,6655,6735,6821,6899,6977,7050,7125,7212,7299,7390,7483,7555,7631,7723,7774,7840,7924,8010,8072,8136,8199,8306,8411,8507,8613,8669,8726", "endLines": "5,33,34,35,36,37,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101", "endColumns": "12,106,104,79,106,99,97,114,82,66,98,67,60,87,62,65,63,70,62,53,108,58,62,53,73,124,89,79,144,82,81,90,51,52,65,70,79,85,77,77,72,74,86,86,90,92,71,75,91,50,65,83,85,61,63,62,106,104,95,105,55,56,82", "endOffsets": "320,3172,3277,3357,3464,3564,4391,4506,4589,4656,4755,4823,4884,4972,5035,5101,5165,5236,5299,5353,5462,5521,5584,5638,5712,5837,5927,6007,6152,6235,6317,6408,6460,6513,6579,6650,6730,6816,6894,6972,7045,7120,7207,7294,7385,7478,7550,7626,7718,7769,7835,7919,8005,8067,8131,8194,8301,8406,8502,8608,8664,8721,8804"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\6fe3e64a80094b4316c7006f0bcdcf47\\transformed\\core-1.10.1\\res\\values-eu\\values-eu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,256,356,459,563,666,784", "endColumns": "97,102,99,102,103,102,117,100", "endOffsets": "148,251,351,454,558,661,779,880"}, "to": {"startLines": "38,39,40,41,42,43,44,103", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3569,3667,3770,3870,3973,4077,4180,8892", "endColumns": "97,102,99,102,103,102,117,100", "endOffsets": "3662,3765,3865,3968,4072,4175,4293,8988"}}]}]}