5com.enhanced.videowidget.ui.adapter.WidgetListAdapterFcom.enhanced.videowidget.ui.adapter.WidgetListAdapter.WidgetViewHolderHcom.enhanced.videowidget.ui.adapter.WidgetListAdapter.WidgetDiffCallback4com.enhanced.videowidget.service.WidgetUpdateService>com.enhanced.videowidget.service.WidgetUpdateService.Companion9com.enhanced.videowidget.data.repository.WidgetRepositoryCcom.enhanced.videowidget.data.repository.WidgetRepository.Companion(com.enhanced.videowidget.utils.AppLogger2com.enhanced.videowidget.utils.VideoFrameExtractor<<EMAIL><com.enhanced.videowidget.utils.VideoFrameExtractor.Companion(com.enhanced.videowidget.ui.MainActivity2com.enhanced.videowidget.ui.MainActivity.Companion7com.enhanced.videowidget.ui.WidgetConfigurationActivityAcom.enhanced.videowidget.ui.WidgetConfigurationActivity.Companion2com.enhanced.videowidget.worker.WidgetUpdateWorker<com.enhanced.videowidget.worker.WidgetUpdateWorker.Companion3com.enhanced.videowidget.widget.EnhancedVideoWidget=com.enhanced.videowidget.widget.EnhancedVideoWidget.Companion*com.enhanced.videowidget.data.model.Widget                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              