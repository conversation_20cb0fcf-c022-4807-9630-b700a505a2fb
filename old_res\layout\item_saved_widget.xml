<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="8dp">
    <ImageView
        android:id="@+id/imageViewWidget"
        android:background="@drawable/round_outline"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:scaleType="centerCrop"
        android:clipToOutline="true"
        app:layout_constraintDimensionRatio="1:1"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>
    <FrameLayout
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="@+id/imageViewWidget"
        app:layout_constraintEnd_toEndOf="@+id/imageViewWidget"
        app:layout_constraintStart_toStartOf="@+id/imageViewWidget"
        app:layout_constraintTop_toTopOf="@+id/imageViewWidget">
        <Button
            android:id="@+id/buttonOverlay"
            android:background="@drawable/ripple_button_background"
            android:focusable="true"
            android:clickable="true"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_margin="0dp"
            android:text=""/>
    </FrameLayout>
</androidx.constraintlayout.widget.ConstraintLayout>
