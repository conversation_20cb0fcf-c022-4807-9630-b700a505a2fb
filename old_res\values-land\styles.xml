<?xml version="1.0" encoding="utf-8"?>
<resources>
    <style name="Widget.Design.TabLayout" parent="@style/Base.Widget.Design.TabLayout">
        <item name="tabGravity">center</item>
        <item name="tabMode">fixed</item>
    </style>
    <style name="Widget.MaterialComponents.MaterialCalendar.HeaderSelection" parent="@style/Widget.AppCompat.TextView">
        <item name="android:textAppearance">?attr/textAppearanceHeadline6</item>
        <item name="android:textColor">?attr/colorOnPrimary</item>
        <item name="android:ellipsize">3</item>
        <item name="android:layout_gravity">0x800033</item>
        <item name="android:layout_width">-1</item>
        <item name="android:layout_height">-2</item>
        <item name="android:maxLines">@integer/mtrl_calendar_selection_text_lines</item>
    </style>
    <style name="Widget.MaterialComponents.MaterialCalendar.HeaderToggleButton" parent="@style/Base.Widget.MaterialComponents.MaterialCalendar.HeaderToggleButton">
        <item name="android:layout_gravity">0x800053</item>
    </style>
</resources>
