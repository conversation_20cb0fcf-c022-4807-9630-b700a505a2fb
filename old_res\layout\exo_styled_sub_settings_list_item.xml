<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="horizontal"
    android:background="?android:attr/selectableItemBackground"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:minWidth="@dimen/exo_setting_width"
    android:minHeight="@dimen/exo_settings_height"
    android:layoutDirection="locale">
    <ImageView
        android:id="@+id/exo_check"
        android:visibility="invisible"
        android:layout_width="@dimen/exo_settings_icon_size"
        android:layout_height="@dimen/exo_settings_icon_size"
        android:layout_marginLeft="8dp"
        android:layout_marginTop="12dp"
        android:layout_marginRight="8dp"
        android:src="@drawable/exo_styled_controls_check"/>
    <TextView
        android:textSize="@dimen/exo_settings_main_text_size"
        android:textColor="@color/exo_white"
        android:gravity="start|center_vertical"
        android:id="@+id/exo_text"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginRight="4dp"
        android:minHeight="@dimen/exo_settings_height"
        android:textDirection="locale"
        android:layout_marginEnd="4dp"/>
</LinearLayout>
