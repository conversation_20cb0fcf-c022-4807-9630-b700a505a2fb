<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@color/background"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <TextView
        android:textSize="44sp"
        android:textColor="@color/textColor"
        android:id="@+id/headerTextView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="88dp"
        android:text="@string/video_widgets"
        android:fontFamily="@font/samsungsharpsans_bold"
        android:translationZ="1dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>
    <com.airbnb.lottie.LottieAnimationView
        android:id="@+id/lottieView"
        android:background="@android:color/transparent"
        android:layout_width="wrap_content"
        android:layout_height="460dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:lottie_autoPlay="true"
        app:lottie_loop="true"
        app:lottie_rawRes="@raw/instruction_video_light"/>
    <TextView
        android:textColor="@color/textColor"
        android:id="@+id/textView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/create_widget"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/lottieView"/>
</androidx.constraintlayout.widget.ConstraintLayout>
