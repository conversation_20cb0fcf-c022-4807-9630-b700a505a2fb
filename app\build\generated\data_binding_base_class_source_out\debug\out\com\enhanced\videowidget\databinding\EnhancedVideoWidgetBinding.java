// Generated by view binder compiler. Do not edit!
package com.enhanced.videowidget.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.ImageButton;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.enhanced.videowidget.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class EnhancedVideoWidgetBinding implements ViewBinding {
  @NonNull
  private final FrameLayout rootView;

  @NonNull
  public final LinearLayout controlOverlay;

  @NonNull
  public final TextView frameCounter;

  @NonNull
  public final ImageView imageVideoView;

  @NonNull
  public final ImageButton playPauseButton;

  @NonNull
  public final ProgressBar progressBar;

  @NonNull
  public final ImageButton settingsButton;

  @NonNull
  public final FrameLayout widgetContainer;

  @NonNull
  public final TextView widgetStatus;

  @NonNull
  public final TextView widgetTitle;

  private EnhancedVideoWidgetBinding(@NonNull FrameLayout rootView,
      @NonNull LinearLayout controlOverlay, @NonNull TextView frameCounter,
      @NonNull ImageView imageVideoView, @NonNull ImageButton playPauseButton,
      @NonNull ProgressBar progressBar, @NonNull ImageButton settingsButton,
      @NonNull FrameLayout widgetContainer, @NonNull TextView widgetStatus,
      @NonNull TextView widgetTitle) {
    this.rootView = rootView;
    this.controlOverlay = controlOverlay;
    this.frameCounter = frameCounter;
    this.imageVideoView = imageVideoView;
    this.playPauseButton = playPauseButton;
    this.progressBar = progressBar;
    this.settingsButton = settingsButton;
    this.widgetContainer = widgetContainer;
    this.widgetStatus = widgetStatus;
    this.widgetTitle = widgetTitle;
  }

  @Override
  @NonNull
  public FrameLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static EnhancedVideoWidgetBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static EnhancedVideoWidgetBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.enhanced_video_widget, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static EnhancedVideoWidgetBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.controlOverlay;
      LinearLayout controlOverlay = ViewBindings.findChildViewById(rootView, id);
      if (controlOverlay == null) {
        break missingId;
      }

      id = R.id.frameCounter;
      TextView frameCounter = ViewBindings.findChildViewById(rootView, id);
      if (frameCounter == null) {
        break missingId;
      }

      id = R.id.imageVideoView;
      ImageView imageVideoView = ViewBindings.findChildViewById(rootView, id);
      if (imageVideoView == null) {
        break missingId;
      }

      id = R.id.playPauseButton;
      ImageButton playPauseButton = ViewBindings.findChildViewById(rootView, id);
      if (playPauseButton == null) {
        break missingId;
      }

      id = R.id.progressBar;
      ProgressBar progressBar = ViewBindings.findChildViewById(rootView, id);
      if (progressBar == null) {
        break missingId;
      }

      id = R.id.settingsButton;
      ImageButton settingsButton = ViewBindings.findChildViewById(rootView, id);
      if (settingsButton == null) {
        break missingId;
      }

      FrameLayout widgetContainer = (FrameLayout) rootView;

      id = R.id.widgetStatus;
      TextView widgetStatus = ViewBindings.findChildViewById(rootView, id);
      if (widgetStatus == null) {
        break missingId;
      }

      id = R.id.widgetTitle;
      TextView widgetTitle = ViewBindings.findChildViewById(rootView, id);
      if (widgetTitle == null) {
        break missingId;
      }

      return new EnhancedVideoWidgetBinding((FrameLayout) rootView, controlOverlay, frameCounter,
          imageVideoView, playPauseButton, progressBar, settingsButton, widgetContainer,
          widgetStatus, widgetTitle);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
