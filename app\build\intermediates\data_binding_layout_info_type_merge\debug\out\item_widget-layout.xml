<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_widget" modulePackage="com.enhanced.videowidget" filePath="app\src\main\res\layout\item_widget.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="com.google.android.material.card.MaterialCardView"><Targets><Target tag="layout/item_widget_0" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="1" startOffset="0" endLine="130" endOffset="51"/></Target><Target id="@+id/imageViewPreview" view="ImageView"><Expressions/><location startLine="17" startOffset="8" endLine="25" endOffset="53"/></Target><Target id="@+id/textViewName" view="TextView"><Expressions/><location startLine="35" startOffset="12" endLine="44" endOffset="45"/></Target><Target id="@+id/textViewDescription" view="TextView"><Expressions/><location startLine="46" startOffset="12" endLine="55" endOffset="44"/></Target><Target id="@+id/imageViewStatus" view="ImageView"><Expressions/><location startLine="65" startOffset="16" endLine="72" endOffset="52"/></Target><Target id="@+id/textViewStatus" view="TextView"><Expressions/><location startLine="74" startOffset="16" endLine="81" endOffset="42"/></Target><Target id="@+id/textViewFrameInfo" view="TextView"><Expressions/><location startLine="84" startOffset="16" endLine="92" endOffset="40"/></Target><Target id="@+id/buttonPlayPause" view="ImageButton"><Expressions/><location startLine="105" startOffset="12" endLine="114" endOffset="39"/></Target><Target id="@+id/buttonDelete" view="ImageButton"><Expressions/><location startLine="116" startOffset="12" endLine="124" endOffset="39"/></Target></Targets></Layout>