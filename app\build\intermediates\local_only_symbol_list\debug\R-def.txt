R_DEF: Internal format may change without notice
local
attr appWidgetInnerRadius
attr appWidgetPadding
attr appWidgetRadius
color background
color backgroundSecondary
color black
color blue
color blue_light
color btn_disabled_color
color purple_200
color purple_500
color purple_700
color teal_200
color teal_700
color textColor
color textColorSecondary
color text_disabled_color
color white
drawable control_overlay_background
drawable delete_button_background
drawable frame_counter_background
drawable ic_add
drawable ic_delete
drawable ic_pause
drawable ic_play
drawable ic_settings
drawable ic_video
drawable ic_video_widget_placeholder
drawable placeholder_video
drawable play_pause_button_background
drawable settings_button_background
drawable widget_background
drawable widget_preview
id buttonCancel
id buttonCreateWidget
id buttonDelete
id buttonPlayPause
id buttonSelectVideo
id controlOverlay
id editTextWidgetName
id fabAddWidget
id frameCounter
id imageVideoView
id imageViewPreview
id imageViewStatus
id playPauseButton
id progressBar
id recyclerViewWidgets
id settingsButton
id textViewDescription
id textViewFrameInfo
id textViewName
id textViewNoWidgets
id textViewSelectedVideo
id textViewStatus
id textViewVideoInfo
id toolbar
id widgetStatus
id widgetTitle
id widget_container
layout activity_main
layout activity_widget_configuration
layout enhanced_video_widget
layout item_widget
mipmap ic_launcher
mipmap ic_launcher_background
mipmap ic_launcher_foreground
mipmap ic_launcher_round
string about
string action_delete
string action_edit
string action_pause
string action_play
string action_settings
string add_widget
string app_name
string auto
string cancel
string cancel_widget
string create_widget
string dark
string default_widget_title
string enable_notifications
string error_file_not_found
string error_unsupported_format
string error_video_processing
string error_widget_creation
string general_settings
string go_to_settings
string light
string load_widget
string loading_status
string main_activity_title
string my_widgets
string no_video_selected
string no_widgets
string notifications
string paused_status
string permission_denied
string permission_granted
string permission_settings_required
string permission_storage_message
string permission_storage_title
string permission_video_required
string play_pause_button_description
string playing_status
string rate
string save_widget
string select_video
string settings
string settings_button_description
string share
string theme
string upload_video
string video_library_access
string video_message
string video_picker_error
string video_selected
string video_widget_content_description
string video_widgets
string widget_configuration_title
string widget_created
string widget_deleted
string widget_list
string widget_provider_description
string widget_provider_label
string widget_settings
style Theme.EnhancedVideoWidget
style Theme.EnhancedVideoWidget.AppWidgetContainer
style Theme.EnhancedVideoWidget.AppWidgetContainerParent
style Theme.EnhancedVideoWidget.NoActionBar
xml backup_rules
xml data_extraction_rules
xml enhanced_video_widget_info
