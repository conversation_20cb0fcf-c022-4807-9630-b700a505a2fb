<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_widget_configuration" modulePackage="com.enhanced.videowidget" filePath="app\src\main\res\layout\activity_widget_configuration.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.coordinatorlayout.widget.CoordinatorLayout"><Targets><Target tag="layout/activity_widget_configuration_0" view="androidx.coordinatorlayout.widget.CoordinatorLayout"><Expressions/><location startLine="1" startOffset="0" endLine="141" endOffset="53"/></Target><Target id="@+id/toolbar" view="androidx.appcompat.widget.Toolbar"><Expressions/><location startLine="13" startOffset="8" endLine="18" endOffset="66"/></Target><Target id="@+id/editTextWidgetName" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="41" startOffset="16" endLine="46" endOffset="42"/></Target><Target id="@+id/buttonSelectVideo" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="59" startOffset="12" endLine="67" endOffset="80"/></Target><Target id="@+id/textViewSelectedVideo" view="TextView"><Expressions/><location startLine="69" startOffset="12" endLine="76" endOffset="52"/></Target><Target id="@+id/imageViewPreview" view="ImageView"><Expressions/><location startLine="79" startOffset="12" endLine="90" endOffset="57"/></Target><Target id="@+id/textViewVideoInfo" view="TextView"><Expressions/><location startLine="92" startOffset="12" endLine="102" endOffset="55"/></Target><Target id="@+id/buttonCancel" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="111" startOffset="16" endLine="117" endOffset="80"/></Target><Target id="@+id/buttonCreateWidget" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="119" startOffset="16" endLine="124" endOffset="45"/></Target><Target id="@+id/progressBar" view="ProgressBar"><Expressions/><location startLine="133" startOffset="4" endLine="139" endOffset="53"/></Target></Targets></Layout>