<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@color/background"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:backgroundTint="@color/background">
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recyclerViewWidgets"
        android:paddingLeft="8dp"
        android:paddingRight="8dp"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:paddingHorizontal="8dp"
        app:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
        app:layout_behavior="@string/appbar_scrolling_view_behavior"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="parent"
        app:spanCount="2"/>
    <TextView
        android:textColor="#bcbcbc"
        android:id="@+id/emptyListText"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="120dp"
        android:text="@string/empty_list"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>
</androidx.constraintlayout.widget.ConstraintLayout>
