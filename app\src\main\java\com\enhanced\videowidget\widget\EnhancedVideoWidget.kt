package com.enhanced.videowidget.widget

import android.app.PendingIntent
import android.appwidget.AppWidgetManager
import android.appwidget.AppWidgetProvider
import android.content.Context
import android.content.Intent
import android.graphics.Bitmap
import android.util.Log
import android.widget.RemoteViews
import androidx.work.Data
import androidx.work.ExistingWorkPolicy
import androidx.work.OneTimeWorkRequest
import androidx.work.OutOfQuotaPolicy
import androidx.work.WorkManager
import com.enhanced.videowidget.R
import com.enhanced.videowidget.data.model.Widget
import com.enhanced.videowidget.data.repository.WidgetRepository
import com.enhanced.videowidget.service.WidgetUpdateService
import com.enhanced.videowidget.worker.WidgetUpdateWorker

/**
 * Enhanced Video Widget Provider with pause/play functionality
 */
class EnhancedVideoWidget : AppWidgetProvider() {
    
    companion object {
        private const val TAG = "EnhancedVideoWidget"
        const val ACTION_WIDGET_TAP = "com.enhanced.videowidget.ACTION_WIDGET_TAP"
        const val ACTION_PLAY_PAUSE = "com.enhanced.videowidget.ACTION_PLAY_PAUSE"
        const val ACTION_SETTINGS = "com.enhanced.videowidget.ACTION_SETTINGS"
        const val EXTRA_WIDGET_ID = "widget_id"
        
        fun updateAppWidget(
            context: Context,
            appWidgetManager: AppWidgetManager,
            appWidgetId: Int,
            widget: Widget? = null,
            frameBitmap: Bitmap? = null
        ) {
            val repository = WidgetRepository.getInstance()
            val widgetData = widget ?: repository.getWidget(appWidgetId)
            
            val views = RemoteViews(context.packageName, R.layout.enhanced_video_widget)
            
            // Update video/image display
            if (frameBitmap != null) {
                views.setImageViewBitmap(R.id.imageVideoView, frameBitmap)
                views.setViewVisibility(R.id.progressBar, android.view.View.GONE)
            } else {
                views.setViewVisibility(R.id.progressBar, android.view.View.VISIBLE)
            }
            
            // Update widget info
            widgetData?.let { w ->
                views.setTextViewText(R.id.widgetTitle, w.name)
                views.setTextViewText(R.id.widgetStatus, 
                    if (w.isPaused) context.getString(R.string.paused_status) 
                    else context.getString(R.string.playing_status)
                )
                
                // Update play/pause button
                val playPauseIcon = if (w.isPaused) R.drawable.ic_play else R.drawable.ic_pause
                views.setImageViewResource(R.id.playPauseButton, playPauseIcon)
                
                // Update frame counter (for debugging)
                views.setTextViewText(R.id.frameCounter, "${w.currentFrameIndex}/${w.totalFrames}")
            }
            
            // Set up click listeners
            setupClickListeners(context, views, appWidgetId)
            
            // Update the widget
            appWidgetManager.updateAppWidget(appWidgetId, views)
        }
        
        private fun setupClickListeners(context: Context, views: RemoteViews, appWidgetId: Int) {
            // Play/Pause button click
            val playPauseIntent = Intent(context, EnhancedVideoWidget::class.java).apply {
                action = ACTION_PLAY_PAUSE
                putExtra(EXTRA_WIDGET_ID, appWidgetId)
            }
            val playPausePendingIntent = PendingIntent.getBroadcast(
                context, 
                appWidgetId * 10 + 1, 
                playPauseIntent, 
                PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
            )
            views.setOnClickPendingIntent(R.id.playPauseButton, playPausePendingIntent)
            
            // Settings button click
            val settingsIntent = Intent(context, EnhancedVideoWidget::class.java).apply {
                action = ACTION_SETTINGS
                putExtra(EXTRA_WIDGET_ID, appWidgetId)
            }
            val settingsPendingIntent = PendingIntent.getBroadcast(
                context, 
                appWidgetId * 10 + 2, 
                settingsIntent, 
                PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
            )
            views.setOnClickPendingIntent(R.id.settingsButton, settingsPendingIntent)
            
            // Widget container click (for general interaction)
            val widgetIntent = Intent(context, EnhancedVideoWidget::class.java).apply {
                action = ACTION_WIDGET_TAP
                putExtra(EXTRA_WIDGET_ID, appWidgetId)
            }
            val widgetPendingIntent = PendingIntent.getBroadcast(
                context, 
                appWidgetId * 10 + 3, 
                widgetIntent, 
                PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
            )
            views.setOnClickPendingIntent(R.id.widget_container, widgetPendingIntent)
        }
    }
    
    override fun onReceive(context: Context, intent: Intent) {
        super.onReceive(context, intent)
        
        val repository = WidgetRepository.getInstance()
        repository.initialize(context)
        
        val widgetId = intent.getIntExtra(EXTRA_WIDGET_ID, -1)
        if (widgetId == -1) return
        
        when (intent.action) {
            ACTION_PLAY_PAUSE -> {
                Log.d(TAG, "Play/Pause action for widget $widgetId")
                handlePlayPauseAction(context, widgetId)
            }
            ACTION_SETTINGS -> {
                Log.d(TAG, "Settings action for widget $widgetId")
                handleSettingsAction(context, widgetId)
            }
            ACTION_WIDGET_TAP -> {
                Log.d(TAG, "Widget tap action for widget $widgetId")
                handleWidgetTapAction(context, widgetId)
            }
        }
    }
    
    private fun handlePlayPauseAction(context: Context, widgetId: Int) {
        val repository = WidgetRepository.getInstance()
        val updatedWidget = repository.toggleWidgetPlayPause(widgetId)
        
        updatedWidget?.let { widget ->
            // Update widget UI immediately
            val appWidgetManager = AppWidgetManager.getInstance(context)
            updateAppWidget(context, appWidgetManager, widgetId, widget)
            
            // Start or stop the update service based on play state
            if (widget.shouldAnimate()) {
                startWidgetUpdateWork(context, widgetId)
            } else {
                stopWidgetUpdateWork(context, widgetId)
            }
        }
    }
    
    private fun handleSettingsAction(context: Context, widgetId: Int) {
        // Open widget configuration activity
        // This would typically open a settings screen
        Log.d(TAG, "Settings action not implemented yet for widget $widgetId")
    }
    
    private fun handleWidgetTapAction(context: Context, widgetId: Int) {
        // Handle general widget tap - could show info or toggle play/pause
        handlePlayPauseAction(context, widgetId)
    }
    
    override fun onUpdate(context: Context, appWidgetManager: AppWidgetManager, appWidgetIds: IntArray) {
        Log.d(TAG, "onUpdate called for widgets: ${appWidgetIds.contentToString()}")
        
        val repository = WidgetRepository.getInstance()
        repository.initialize(context)
        
        for (appWidgetId in appWidgetIds) {
            val widget = repository.getWidget(appWidgetId)
            
            if (widget != null) {
                // Update widget UI
                updateAppWidget(context, appWidgetManager, appWidgetId, widget)
                
                // Start animation if widget should be playing
                if (widget.shouldAnimate()) {
                    startWidgetUpdateWork(context, appWidgetId)
                }
            } else {
                // Widget not found in repository, show placeholder
                updateAppWidget(context, appWidgetManager, appWidgetId)
            }
        }
    }
    
    override fun onDeleted(context: Context, appWidgetIds: IntArray) {
        Log.d(TAG, "onDeleted called for widgets: ${appWidgetIds.contentToString()}")
        
        val repository = WidgetRepository.getInstance()
        repository.initialize(context)
        
        for (appWidgetId in appWidgetIds) {
            // Stop any running work for this widget
            stopWidgetUpdateWork(context, appWidgetId)
            
            // Remove widget from repository
            repository.removeWidget(appWidgetId)
        }
    }
    
    override fun onEnabled(context: Context) {
        Log.d(TAG, "Widget provider enabled")
        val repository = WidgetRepository.getInstance()
        repository.initialize(context)
    }
    
    override fun onDisabled(context: Context) {
        Log.d(TAG, "Widget provider disabled")
        // Stop all widget update work
        WorkManager.getInstance(context).cancelAllWorkByTag("widget_update")
    }
    
    private fun startWidgetUpdateWork(context: Context, appWidgetId: Int) {
        val inputData = Data.Builder()
            .putInt(EXTRA_WIDGET_ID, appWidgetId)
            .build()
        
        val workRequest = OneTimeWorkRequest.Builder(WidgetUpdateWorker::class.java)
            .setInputData(inputData)
            .setExpedited(OutOfQuotaPolicy.RUN_AS_NON_EXPEDITED_WORK_REQUEST)
            .addTag("widget_update")
            .addTag("widget_$appWidgetId")
            .build()
        
        WorkManager.getInstance(context).enqueueUniqueWork(
            "widget_update_$appWidgetId",
            ExistingWorkPolicy.REPLACE,
            workRequest
        )
    }
    
    private fun stopWidgetUpdateWork(context: Context, appWidgetId: Int) {
        WorkManager.getInstance(context).cancelUniqueWork("widget_update_$appWidgetId")
    }
}
