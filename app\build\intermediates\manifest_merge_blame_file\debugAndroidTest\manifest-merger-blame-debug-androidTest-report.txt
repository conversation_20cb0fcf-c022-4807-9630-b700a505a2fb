1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.enhanced.videowidget.test" >
4
5    <uses-sdk
5-->D:\php\android\enhanced_video_widget_app\app\build\intermediates\tmp\manifest\androidTest\debug\tempFile1ProcessTestManifest3728770681196414405.xml:5:5-74
6        android:minSdkVersion="24"
6-->D:\php\android\enhanced_video_widget_app\app\build\intermediates\tmp\manifest\androidTest\debug\tempFile1ProcessTestManifest3728770681196414405.xml:5:15-41
7        android:targetSdkVersion="33" />
7-->D:\php\android\enhanced_video_widget_app\app\build\intermediates\tmp\manifest\androidTest\debug\tempFile1ProcessTestManifest3728770681196414405.xml:5:42-71
8
9    <instrumentation
9-->D:\php\android\enhanced_video_widget_app\app\build\intermediates\tmp\manifest\androidTest\debug\tempFile1ProcessTestManifest3728770681196414405.xml:11:5-15:74
10        android:name="androidx.test.runner.AndroidJUnitRunner"
10-->D:\php\android\enhanced_video_widget_app\app\build\intermediates\tmp\manifest\androidTest\debug\tempFile1ProcessTestManifest3728770681196414405.xml:11:22-76
11        android:functionalTest="false"
11-->D:\php\android\enhanced_video_widget_app\app\build\intermediates\tmp\manifest\androidTest\debug\tempFile1ProcessTestManifest3728770681196414405.xml:14:22-52
12        android:handleProfiling="false"
12-->D:\php\android\enhanced_video_widget_app\app\build\intermediates\tmp\manifest\androidTest\debug\tempFile1ProcessTestManifest3728770681196414405.xml:13:22-53
13        android:label="Tests for com.enhanced.videowidget"
13-->D:\php\android\enhanced_video_widget_app\app\build\intermediates\tmp\manifest\androidTest\debug\tempFile1ProcessTestManifest3728770681196414405.xml:15:22-72
14        android:targetPackage="com.enhanced.videowidget" />
14-->D:\php\android\enhanced_video_widget_app\app\build\intermediates\tmp\manifest\androidTest\debug\tempFile1ProcessTestManifest3728770681196414405.xml:12:22-70
15
16    <uses-permission android:name="android.permission.REORDER_TASKS" />
16-->[androidx.test:core:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\ffb3a32e869fb334f0010383b205f20b\transformed\core-1.5.0\AndroidManifest.xml:24:5-72
16-->[androidx.test:core:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\ffb3a32e869fb334f0010383b205f20b\transformed\core-1.5.0\AndroidManifest.xml:24:22-69
17
18    <queries>
18-->[androidx.test:runner:1.5.2] C:\Users\<USER>\.gradle\caches\transforms-3\3591b587422505d6cc6be111a1757c0e\transformed\runner-1.5.2\AndroidManifest.xml:24:5-28:15
19        <package android:name="androidx.test.orchestrator" />
19-->[androidx.test:runner:1.5.2] C:\Users\<USER>\.gradle\caches\transforms-3\3591b587422505d6cc6be111a1757c0e\transformed\runner-1.5.2\AndroidManifest.xml:25:9-62
19-->[androidx.test:runner:1.5.2] C:\Users\<USER>\.gradle\caches\transforms-3\3591b587422505d6cc6be111a1757c0e\transformed\runner-1.5.2\AndroidManifest.xml:25:18-59
20        <package android:name="androidx.test.services" />
20-->[androidx.test:runner:1.5.2] C:\Users\<USER>\.gradle\caches\transforms-3\3591b587422505d6cc6be111a1757c0e\transformed\runner-1.5.2\AndroidManifest.xml:26:9-58
20-->[androidx.test:runner:1.5.2] C:\Users\<USER>\.gradle\caches\transforms-3\3591b587422505d6cc6be111a1757c0e\transformed\runner-1.5.2\AndroidManifest.xml:26:18-55
21        <package android:name="com.google.android.apps.common.testing.services" />
21-->[androidx.test:runner:1.5.2] C:\Users\<USER>\.gradle\caches\transforms-3\3591b587422505d6cc6be111a1757c0e\transformed\runner-1.5.2\AndroidManifest.xml:27:9-83
21-->[androidx.test:runner:1.5.2] C:\Users\<USER>\.gradle\caches\transforms-3\3591b587422505d6cc6be111a1757c0e\transformed\runner-1.5.2\AndroidManifest.xml:27:18-80
22    </queries>
23
24    <application
24-->D:\php\android\enhanced_video_widget_app\app\build\intermediates\tmp\manifest\androidTest\debug\tempFile1ProcessTestManifest3728770681196414405.xml:7:5-9:19
25        android:debuggable="true"
26        android:extractNativeLibs="false" >
27        <uses-library android:name="android.test.runner" />
27-->D:\php\android\enhanced_video_widget_app\app\build\intermediates\tmp\manifest\androidTest\debug\tempFile1ProcessTestManifest3728770681196414405.xml:8:9-60
27-->D:\php\android\enhanced_video_widget_app\app\build\intermediates\tmp\manifest\androidTest\debug\tempFile1ProcessTestManifest3728770681196414405.xml:8:23-57
28
29        <activity
29-->[androidx.test:core:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\ffb3a32e869fb334f0010383b205f20b\transformed\core-1.5.0\AndroidManifest.xml:27:9-34:20
30            android:name="androidx.test.core.app.InstrumentationActivityInvoker$BootstrapActivity"
30-->[androidx.test:core:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\ffb3a32e869fb334f0010383b205f20b\transformed\core-1.5.0\AndroidManifest.xml:28:13-99
31            android:exported="true"
31-->[androidx.test:core:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\ffb3a32e869fb334f0010383b205f20b\transformed\core-1.5.0\AndroidManifest.xml:29:13-36
32            android:theme="@style/WhiteBackgroundTheme" >
32-->[androidx.test:core:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\ffb3a32e869fb334f0010383b205f20b\transformed\core-1.5.0\AndroidManifest.xml:30:13-56
33            <intent-filter android:priority="-100" >
33-->[androidx.test:core:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\ffb3a32e869fb334f0010383b205f20b\transformed\core-1.5.0\AndroidManifest.xml:31:13-33:29
33-->[androidx.test:core:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\ffb3a32e869fb334f0010383b205f20b\transformed\core-1.5.0\AndroidManifest.xml:31:28-51
34                <category android:name="android.intent.category.LAUNCHER" />
34-->[androidx.test:core:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\ffb3a32e869fb334f0010383b205f20b\transformed\core-1.5.0\AndroidManifest.xml:32:17-77
34-->[androidx.test:core:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\ffb3a32e869fb334f0010383b205f20b\transformed\core-1.5.0\AndroidManifest.xml:32:27-74
35            </intent-filter>
36        </activity>
37        <activity
37-->[androidx.test:core:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\ffb3a32e869fb334f0010383b205f20b\transformed\core-1.5.0\AndroidManifest.xml:35:9-42:20
38            android:name="androidx.test.core.app.InstrumentationActivityInvoker$EmptyActivity"
38-->[androidx.test:core:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\ffb3a32e869fb334f0010383b205f20b\transformed\core-1.5.0\AndroidManifest.xml:36:13-95
39            android:exported="true"
39-->[androidx.test:core:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\ffb3a32e869fb334f0010383b205f20b\transformed\core-1.5.0\AndroidManifest.xml:37:13-36
40            android:theme="@style/WhiteBackgroundTheme" >
40-->[androidx.test:core:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\ffb3a32e869fb334f0010383b205f20b\transformed\core-1.5.0\AndroidManifest.xml:38:13-56
41            <intent-filter android:priority="-100" >
41-->[androidx.test:core:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\ffb3a32e869fb334f0010383b205f20b\transformed\core-1.5.0\AndroidManifest.xml:31:13-33:29
41-->[androidx.test:core:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\ffb3a32e869fb334f0010383b205f20b\transformed\core-1.5.0\AndroidManifest.xml:31:28-51
42                <category android:name="android.intent.category.LAUNCHER" />
42-->[androidx.test:core:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\ffb3a32e869fb334f0010383b205f20b\transformed\core-1.5.0\AndroidManifest.xml:32:17-77
42-->[androidx.test:core:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\ffb3a32e869fb334f0010383b205f20b\transformed\core-1.5.0\AndroidManifest.xml:32:27-74
43            </intent-filter>
44        </activity>
45        <activity
45-->[androidx.test:core:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\ffb3a32e869fb334f0010383b205f20b\transformed\core-1.5.0\AndroidManifest.xml:43:9-50:20
46            android:name="androidx.test.core.app.InstrumentationActivityInvoker$EmptyFloatingActivity"
46-->[androidx.test:core:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\ffb3a32e869fb334f0010383b205f20b\transformed\core-1.5.0\AndroidManifest.xml:44:13-103
47            android:exported="true"
47-->[androidx.test:core:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\ffb3a32e869fb334f0010383b205f20b\transformed\core-1.5.0\AndroidManifest.xml:45:13-36
48            android:theme="@style/WhiteBackgroundDialogTheme" >
48-->[androidx.test:core:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\ffb3a32e869fb334f0010383b205f20b\transformed\core-1.5.0\AndroidManifest.xml:46:13-62
49            <intent-filter android:priority="-100" >
49-->[androidx.test:core:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\ffb3a32e869fb334f0010383b205f20b\transformed\core-1.5.0\AndroidManifest.xml:31:13-33:29
49-->[androidx.test:core:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\ffb3a32e869fb334f0010383b205f20b\transformed\core-1.5.0\AndroidManifest.xml:31:28-51
50                <category android:name="android.intent.category.LAUNCHER" />
50-->[androidx.test:core:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\ffb3a32e869fb334f0010383b205f20b\transformed\core-1.5.0\AndroidManifest.xml:32:17-77
50-->[androidx.test:core:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\ffb3a32e869fb334f0010383b205f20b\transformed\core-1.5.0\AndroidManifest.xml:32:27-74
51            </intent-filter>
52        </activity>
53    </application>
54
55</manifest>
