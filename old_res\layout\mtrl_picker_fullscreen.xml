<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:gravity="center_horizontal"
    android:orientation="vertical"
    android:id="@+id/mtrl_picker_fullscreen"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <include layout="@layout/mtrl_picker_header_fullscreen"/>
    <androidx.fragment.app.FragmentContainerView
        android:id="@+id/mtrl_calendar_frame"
        android:paddingLeft="@dimen/mtrl_calendar_content_padding"
        android:paddingRight="@dimen/mtrl_calendar_content_padding"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingStart="@dimen/mtrl_calendar_content_padding"
        android:paddingEnd="@dimen/mtrl_calendar_content_padding"/>
</LinearLayout>
