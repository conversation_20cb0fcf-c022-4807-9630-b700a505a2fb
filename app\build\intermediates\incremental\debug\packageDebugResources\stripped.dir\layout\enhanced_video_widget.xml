<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/widget_container"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/widget_background"
    android:padding="8dp">

    <!-- Video/Image Display -->
    <ImageView
        android:id="@+id/imageVideoView"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_gravity="center"
        android:scaleType="centerCrop"
        android:contentDescription="@string/video_widget_content_description"
        tools:src="@drawable/placeholder_video" />

    <!-- Loading Progress Bar -->
    <ProgressBar
        android:id="@+id/progressBar"
        android:layout_width="32dp"
        android:layout_height="32dp"
        android:layout_gravity="center"
        android:indeterminate="true"
        android:visibility="gone"
        style="@style/Widget.AppCompat.ProgressBar" />

    <!-- Control Overlay -->
    <LinearLayout
        android:id="@+id/controlOverlay"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom"
        android:orientation="horizontal"
        android:background="@drawable/control_overlay_background"
        android:padding="8dp"
        android:gravity="center_vertical">

        <!-- Play/Pause Button -->
        <ImageButton
            android:id="@+id/playPauseButton"
            android:layout_width="36dp"
            android:layout_height="36dp"
            android:layout_marginEnd="8dp"
            android:background="@drawable/play_pause_button_background"
            android:src="@drawable/ic_pause"
            android:contentDescription="@string/play_pause_button_description"
            android:scaleType="centerInside"
            android:padding="8dp" />

        <!-- Widget Info -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical">

            <TextView
                android:id="@+id/widgetTitle"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/default_widget_title"
                android:textColor="@android:color/white"
                android:textSize="12sp"
                android:textStyle="bold"
                android:maxLines="1"
                android:ellipsize="end" />

            <TextView
                android:id="@+id/widgetStatus"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/playing_status"
                android:textColor="@android:color/white"
                android:textSize="10sp"
                android:alpha="0.8"
                android:maxLines="1"
                android:ellipsize="end" />

        </LinearLayout>

        <!-- Settings Button -->
        <ImageButton
            android:id="@+id/settingsButton"
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:background="@drawable/settings_button_background"
            android:src="@drawable/ic_settings"
            android:contentDescription="@string/settings_button_description"
            android:scaleType="centerInside"
            android:padding="6dp" />

    </LinearLayout>

    <!-- Frame Counter (for debugging, can be hidden in production) -->
    <TextView
        android:id="@+id/frameCounter"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="top|end"
        android:layout_margin="4dp"
        android:background="@drawable/frame_counter_background"
        android:padding="4dp"
        android:text="0/0"
        android:textColor="@android:color/white"
        android:textSize="8sp"
        android:visibility="gone"
        tools:visibility="visible" />

</FrameLayout>
