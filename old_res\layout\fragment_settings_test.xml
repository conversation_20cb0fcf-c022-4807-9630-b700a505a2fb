<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@color/background"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <com.google.android.material.button.MaterialButton
        android:id="@+id/backBtn"
        android:background="?attr/selectableItemBackgroundBorderless"
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:contentDescription="Back"
        android:layout_marginStart="12dp"
        android:layout_marginEnd="6dp"
        app:icon="@drawable/ic_back"
        app:iconGravity="textStart"
        app:iconPadding="-4dp"
        app:iconSize="20dp"
        app:iconTint="@color/textColor"
        app:layout_constraintBottom_toBottomOf="@+id/headerText"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/headerText"
        style="@style/Widget.MaterialComponents.Button.Icon"/>
    <TextView
        android:textSize="32sp"
        android:textStyle="bold"
        android:textColor="@color/textColor"
        android:id="@+id/headerText"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="44dp"
        android:text="Settings"
        android:layout_marginStart="12dp"
        app:layout_constraintStart_toEndOf="@+id/backBtn"
        app:layout_constraintTop_toTopOf="parent"/>
    <LinearLayout
        android:orientation="vertical"
        android:id="@+id/optionsList"
        android:paddingTop="24dp"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/headerText">
        <com.google.android.material.card.MaterialCardView
            android:id="@+id/themeOptionCard"
            android:focusable="true"
            android:clickable="true"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:layout_marginBottom="4dp"
            android:layout_marginVertical="4dp"
            app:cardBackgroundColor="@android:color/transparent"
            app:cardElevation="0dp"
            app:rippleColor="?attr/colorControlHighlight"
            app:strokeWidth="0dp">
            <LinearLayout
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:padding="16dp"
                android:layout_width="match_parent"
                android:layout_height="wrap_content">
                <ImageView
                    android:id="@+id/themeIcon"
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:src="@drawable/ic_theme"
                    android:layout_marginEnd="16dp"
                    app:tint="#d000ff"/>
                <TextView
                    android:textSize="16sp"
                    android:textColor="@color/textColor"
                    android:id="@+id/themeText"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:text="Theme"
                    android:layout_weight="1"/>
            </LinearLayout>
        </com.google.android.material.card.MaterialCardView>
        <com.google.android.material.card.MaterialCardView
            android:id="@+id/languageOptionCard"
            android:focusable="true"
            android:clickable="true"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:layout_marginBottom="4dp"
            android:layout_marginVertical="4dp"
            app:cardBackgroundColor="@android:color/transparent"
            app:cardElevation="0dp"
            app:rippleColor="?attr/colorControlHighlight"
            app:strokeWidth="0dp">
            <LinearLayout
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:padding="16dp"
                android:layout_width="match_parent"
                android:layout_height="wrap_content">
                <ImageView
                    android:id="@+id/languageIcon"
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:src="@drawable/ic_language"
                    android:layout_marginEnd="16dp"
                    app:tint="#d000ff"/>
                <TextView
                    android:textSize="16sp"
                    android:textColor="@color/textColor"
                    android:id="@+id/languageText"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:text="Language"
                    android:layout_weight="1"/>
            </LinearLayout>
        </com.google.android.material.card.MaterialCardView>
        <com.google.android.material.card.MaterialCardView
            android:id="@+id/legalOptionCard"
            android:focusable="true"
            android:clickable="true"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:layout_marginBottom="4dp"
            android:layout_marginVertical="4dp"
            app:cardBackgroundColor="@android:color/transparent"
            app:cardElevation="0dp"
            app:rippleColor="?attr/colorControlHighlight"
            app:strokeWidth="0dp">
            <LinearLayout
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:padding="16dp"
                android:layout_width="match_parent"
                android:layout_height="wrap_content">
                <ImageView
                    android:id="@+id/legalIcon"
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:src="@drawable/ic_legal"
                    android:layout_marginEnd="16dp"
                    app:tint="#d000ff"/>
                <TextView
                    android:textSize="16sp"
                    android:textColor="@color/textColor"
                    android:id="@+id/legalText"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:text="Legal"
                    android:layout_weight="1"/>
            </LinearLayout>
        </com.google.android.material.card.MaterialCardView>
    </LinearLayout>
    <TextView
        android:textSize="14sp"
        android:textColor="#bcbcbc"
        android:id="@+id/versionInfo"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp"
        android:text="v1.6"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"/>
</androidx.constraintlayout.widget.ConstraintLayout>
