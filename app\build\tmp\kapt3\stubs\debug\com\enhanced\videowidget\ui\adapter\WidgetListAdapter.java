package com.enhanced.videowidget.ui.adapter;

import java.lang.System;

/**
 * Adapter for displaying widget list in RecyclerView
 */
@kotlin.Metadata(mv = {1, 8, 0}, k = 1, d1 = {"\u0000.\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0002\b\u0005\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0004\u0018\u00002\u0012\u0012\u0004\u0012\u00020\u0002\u0012\b\u0012\u00060\u0003R\u00020\u00000\u0001:\u0002\u0011\u0012B-\u0012\u0012\u0010\u0004\u001a\u000e\u0012\u0004\u0012\u00020\u0002\u0012\u0004\u0012\u00020\u00060\u0005\u0012\u0012\u0010\u0007\u001a\u000e\u0012\u0004\u0012\u00020\u0002\u0012\u0004\u0012\u00020\u00060\u0005\u00a2\u0006\u0002\u0010\bJ\u001c\u0010\t\u001a\u00020\u00062\n\u0010\n\u001a\u00060\u0003R\u00020\u00002\u0006\u0010\u000b\u001a\u00020\fH\u0016J\u001c\u0010\r\u001a\u00060\u0003R\u00020\u00002\u0006\u0010\u000e\u001a\u00020\u000f2\u0006\u0010\u0010\u001a\u00020\fH\u0016R\u001a\u0010\u0007\u001a\u000e\u0012\u0004\u0012\u00020\u0002\u0012\u0004\u0012\u00020\u00060\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001a\u0010\u0004\u001a\u000e\u0012\u0004\u0012\u00020\u0002\u0012\u0004\u0012\u00020\u00060\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0013"}, d2 = {"Lcom/enhanced/videowidget/ui/adapter/WidgetListAdapter;", "Landroidx/recyclerview/widget/ListAdapter;", "Lcom/enhanced/videowidget/data/model/Widget;", "Lcom/enhanced/videowidget/ui/adapter/WidgetListAdapter$WidgetViewHolder;", "onWidgetClick", "Lkotlin/Function1;", "", "onDeleteClick", "(Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;)V", "onBindViewHolder", "holder", "position", "", "onCreateViewHolder", "parent", "Landroid/view/ViewGroup;", "viewType", "WidgetDiffCallback", "WidgetViewHolder", "app_debug"})
public final class WidgetListAdapter extends androidx.recyclerview.widget.ListAdapter<com.enhanced.videowidget.data.model.Widget, com.enhanced.videowidget.ui.adapter.WidgetListAdapter.WidgetViewHolder> {
    private final kotlin.jvm.functions.Function1<com.enhanced.videowidget.data.model.Widget, kotlin.Unit> onWidgetClick = null;
    private final kotlin.jvm.functions.Function1<com.enhanced.videowidget.data.model.Widget, kotlin.Unit> onDeleteClick = null;
    
    public WidgetListAdapter(@org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function1<? super com.enhanced.videowidget.data.model.Widget, kotlin.Unit> onWidgetClick, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function1<? super com.enhanced.videowidget.data.model.Widget, kotlin.Unit> onDeleteClick) {
        super(null);
    }
    
    @org.jetbrains.annotations.NotNull
    @java.lang.Override
    public com.enhanced.videowidget.ui.adapter.WidgetListAdapter.WidgetViewHolder onCreateViewHolder(@org.jetbrains.annotations.NotNull
    android.view.ViewGroup parent, int viewType) {
        return null;
    }
    
    @java.lang.Override
    public void onBindViewHolder(@org.jetbrains.annotations.NotNull
    com.enhanced.videowidget.ui.adapter.WidgetListAdapter.WidgetViewHolder holder, int position) {
    }
    
    @kotlin.Metadata(mv = {1, 8, 0}, k = 1, d1 = {"\u00006\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\b\u0086\u0004\u0018\u00002\u00020\u0001B\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\u000e\u0010\u0010\u001a\u00020\u00112\u0006\u0010\u0012\u001a\u00020\u0013R\u000e\u0010\u0005\u001a\u00020\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\tX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\n\u001a\u00020\tX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000b\u001a\u00020\fX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\r\u001a\u00020\fX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000e\u001a\u00020\fX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000f\u001a\u00020\fX\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0014"}, d2 = {"Lcom/enhanced/videowidget/ui/adapter/WidgetListAdapter$WidgetViewHolder;", "Landroidx/recyclerview/widget/RecyclerView$ViewHolder;", "itemView", "Landroid/view/View;", "(Lcom/enhanced/videowidget/ui/adapter/WidgetListAdapter;Landroid/view/View;)V", "buttonDelete", "Landroid/widget/ImageButton;", "buttonPlayPause", "imageViewPreview", "Landroid/widget/ImageView;", "imageViewStatus", "textViewDescription", "Landroid/widget/TextView;", "textViewFrameInfo", "textViewName", "textViewStatus", "bind", "", "widget", "Lcom/enhanced/videowidget/data/model/Widget;", "app_debug"})
    public final class WidgetViewHolder extends androidx.recyclerview.widget.RecyclerView.ViewHolder {
        private final android.widget.TextView textViewName = null;
        private final android.widget.TextView textViewDescription = null;
        private final android.widget.TextView textViewStatus = null;
        private final android.widget.TextView textViewFrameInfo = null;
        private final android.widget.ImageView imageViewPreview = null;
        private final android.widget.ImageView imageViewStatus = null;
        private final android.widget.ImageButton buttonPlayPause = null;
        private final android.widget.ImageButton buttonDelete = null;
        
        public WidgetViewHolder(@org.jetbrains.annotations.NotNull
        android.view.View itemView) {
            super(null);
        }
        
        public final void bind(@org.jetbrains.annotations.NotNull
        com.enhanced.videowidget.data.model.Widget widget) {
        }
    }
    
    @kotlin.Metadata(mv = {1, 8, 0}, k = 1, d1 = {"\u0000\u0018\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0002\b\u0004\b\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00020\u0001B\u0005\u00a2\u0006\u0002\u0010\u0003J\u0018\u0010\u0004\u001a\u00020\u00052\u0006\u0010\u0006\u001a\u00020\u00022\u0006\u0010\u0007\u001a\u00020\u0002H\u0016J\u0018\u0010\b\u001a\u00020\u00052\u0006\u0010\u0006\u001a\u00020\u00022\u0006\u0010\u0007\u001a\u00020\u0002H\u0016\u00a8\u0006\t"}, d2 = {"Lcom/enhanced/videowidget/ui/adapter/WidgetListAdapter$WidgetDiffCallback;", "Landroidx/recyclerview/widget/DiffUtil$ItemCallback;", "Lcom/enhanced/videowidget/data/model/Widget;", "()V", "areContentsTheSame", "", "oldItem", "newItem", "areItemsTheSame", "app_debug"})
    static final class WidgetDiffCallback extends androidx.recyclerview.widget.DiffUtil.ItemCallback<com.enhanced.videowidget.data.model.Widget> {
        
        public WidgetDiffCallback() {
            super();
        }
        
        @java.lang.Override
        public boolean areItemsTheSame(@org.jetbrains.annotations.NotNull
        com.enhanced.videowidget.data.model.Widget oldItem, @org.jetbrains.annotations.NotNull
        com.enhanced.videowidget.data.model.Widget newItem) {
            return false;
        }
        
        @java.lang.Override
        public boolean areContentsTheSame(@org.jetbrains.annotations.NotNull
        com.enhanced.videowidget.data.model.Widget oldItem, @org.jetbrains.annotations.NotNull
        com.enhanced.videowidget.data.model.Widget newItem) {
            return false;
        }
    }
}