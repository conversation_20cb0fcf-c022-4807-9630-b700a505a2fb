<?xml version="1.0" encoding="utf-8"?>
<merger version="3" xmlns:ns1="http://schemas.android.com/tools"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\php\android\enhanced_video_widget_app\app\src\main\res"/><source path="D:\php\android\enhanced_video_widget_app\app\build\generated\res\rs\debug"/><source path="D:\php\android\enhanced_video_widget_app\app\build\generated\res\resValues\debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\php\android\enhanced_video_widget_app\app\src\main\res"><file name="control_overlay_background" path="D:\php\android\enhanced_video_widget_app\app\src\main\res\drawable\control_overlay_background.xml" qualifiers="" type="drawable"/><file name="delete_button_background" path="D:\php\android\enhanced_video_widget_app\app\src\main\res\drawable\delete_button_background.xml" qualifiers="" type="drawable"/><file name="frame_counter_background" path="D:\php\android\enhanced_video_widget_app\app\src\main\res\drawable\frame_counter_background.xml" qualifiers="" type="drawable"/><file name="ic_add" path="D:\php\android\enhanced_video_widget_app\app\src\main\res\drawable\ic_add.xml" qualifiers="" type="drawable"/><file name="ic_delete" path="D:\php\android\enhanced_video_widget_app\app\src\main\res\drawable\ic_delete.xml" qualifiers="" type="drawable"/><file name="ic_pause" path="D:\php\android\enhanced_video_widget_app\app\src\main\res\drawable\ic_pause.xml" qualifiers="" type="drawable"/><file name="ic_play" path="D:\php\android\enhanced_video_widget_app\app\src\main\res\drawable\ic_play.xml" qualifiers="" type="drawable"/><file name="ic_settings" path="D:\php\android\enhanced_video_widget_app\app\src\main\res\drawable\ic_settings.xml" qualifiers="" type="drawable"/><file name="ic_video" path="D:\php\android\enhanced_video_widget_app\app\src\main\res\drawable\ic_video.xml" qualifiers="" type="drawable"/><file name="ic_video_widget_placeholder" path="D:\php\android\enhanced_video_widget_app\app\src\main\res\drawable\ic_video_widget_placeholder.xml" qualifiers="" type="drawable"/><file name="placeholder_video" path="D:\php\android\enhanced_video_widget_app\app\src\main\res\drawable\placeholder_video.xml" qualifiers="" type="drawable"/><file name="play_pause_button_background" path="D:\php\android\enhanced_video_widget_app\app\src\main\res\drawable\play_pause_button_background.xml" qualifiers="" type="drawable"/><file name="settings_button_background" path="D:\php\android\enhanced_video_widget_app\app\src\main\res\drawable\settings_button_background.xml" qualifiers="" type="drawable"/><file name="widget_background" path="D:\php\android\enhanced_video_widget_app\app\src\main\res\drawable\widget_background.xml" qualifiers="" type="drawable"/><file name="widget_preview" path="D:\php\android\enhanced_video_widget_app\app\src\main\res\drawable\widget_preview.xml" qualifiers="" type="drawable"/><file name="activity_main" path="D:\php\android\enhanced_video_widget_app\app\src\main\res\layout\activity_main.xml" qualifiers="" type="layout"/><file name="activity_widget_configuration" path="D:\php\android\enhanced_video_widget_app\app\src\main\res\layout\activity_widget_configuration.xml" qualifiers="" type="layout"/><file name="enhanced_video_widget" path="D:\php\android\enhanced_video_widget_app\app\src\main\res\layout\enhanced_video_widget.xml" qualifiers="" type="layout"/><file name="item_widget" path="D:\php\android\enhanced_video_widget_app\app\src\main\res\layout\item_widget.xml" qualifiers="" type="layout"/><file name="ic_launcher" path="D:\php\android\enhanced_video_widget_app\app\src\main\res\mipmap-anydpi-v26\ic_launcher.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher_round" path="D:\php\android\enhanced_video_widget_app\app\src\main\res\mipmap-anydpi-v26\ic_launcher_round.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher" path="D:\php\android\enhanced_video_widget_app\app\src\main\res\mipmap-hdpi\ic_launcher.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher_background" path="D:\php\android\enhanced_video_widget_app\app\src\main\res\mipmap-hdpi\ic_launcher_background.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher_foreground" path="D:\php\android\enhanced_video_widget_app\app\src\main\res\mipmap-hdpi\ic_launcher_foreground.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\php\android\enhanced_video_widget_app\app\src\main\res\mipmap-hdpi\ic_launcher_round.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\php\android\enhanced_video_widget_app\app\src\main\res\mipmap-mdpi\ic_launcher.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher_background" path="D:\php\android\enhanced_video_widget_app\app\src\main\res\mipmap-mdpi\ic_launcher_background.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher_foreground" path="D:\php\android\enhanced_video_widget_app\app\src\main\res\mipmap-mdpi\ic_launcher_foreground.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\php\android\enhanced_video_widget_app\app\src\main\res\mipmap-mdpi\ic_launcher_round.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\php\android\enhanced_video_widget_app\app\src\main\res\mipmap-xhdpi\ic_launcher.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher_background" path="D:\php\android\enhanced_video_widget_app\app\src\main\res\mipmap-xhdpi\ic_launcher_background.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher_foreground" path="D:\php\android\enhanced_video_widget_app\app\src\main\res\mipmap-xhdpi\ic_launcher_foreground.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\php\android\enhanced_video_widget_app\app\src\main\res\mipmap-xhdpi\ic_launcher_round.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\php\android\enhanced_video_widget_app\app\src\main\res\mipmap-xxhdpi\ic_launcher.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher_background" path="D:\php\android\enhanced_video_widget_app\app\src\main\res\mipmap-xxhdpi\ic_launcher_background.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher_foreground" path="D:\php\android\enhanced_video_widget_app\app\src\main\res\mipmap-xxhdpi\ic_launcher_foreground.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\php\android\enhanced_video_widget_app\app\src\main\res\mipmap-xxhdpi\ic_launcher_round.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\php\android\enhanced_video_widget_app\app\src\main\res\mipmap-xxxhdpi\ic_launcher.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="ic_launcher_background" path="D:\php\android\enhanced_video_widget_app\app\src\main\res\mipmap-xxxhdpi\ic_launcher_background.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="ic_launcher_foreground" path="D:\php\android\enhanced_video_widget_app\app\src\main\res\mipmap-xxxhdpi\ic_launcher_foreground.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\php\android\enhanced_video_widget_app\app\src\main\res\mipmap-xxxhdpi\ic_launcher_round.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file path="D:\php\android\enhanced_video_widget_app\app\src\main\res\values\attrs.xml" qualifiers=""><attr format="dimension" name="appWidgetInnerRadius">
    </attr><attr format="dimension" name="appWidgetPadding">
    </attr><attr format="dimension" name="appWidgetRadius">
    </attr></file><file path="D:\php\android\enhanced_video_widget_app\app\src\main\res\values\colors.xml" qualifiers=""><color name="purple_200">#FFBB86FC</color><color name="purple_500">#FF6200EE</color><color name="purple_700">#FF3700B3</color><color name="teal_200">#FF03DAC5</color><color name="teal_700">#FF018786</color><color name="black">#FF000000</color><color name="white">#FFFFFFFF</color><color name="background">#f6f6f6</color><color name="backgroundSecondary">#ffffff</color><color name="blue">#1885f2</color><color name="blue_light">#b0d5f2</color><color name="textColor">#252525</color><color name="textColorSecondary">#6b6b7a</color><color name="text_disabled_color">#bfbfbf</color><color name="btn_disabled_color">#e7e7e7</color></file><file path="D:\php\android\enhanced_video_widget_app\app\src\main\res\values\strings.xml" qualifiers=""><string name="app_name">Enhanced Video Widget</string><string name="video_widget_content_description">Video widget display</string><string name="play_pause_button_description">Play or pause video</string><string name="settings_button_description">Widget settings</string><string name="default_widget_title">Video Widget</string><string name="playing_status">Playing</string><string name="paused_status">Paused</string><string name="loading_status">Loading...</string><string name="widget_configuration_title">Configure Video Widget</string><string name="select_video">Select Video</string><string name="video_selected">Video selected</string><string name="no_video_selected">No video selected</string><string name="cancel">Cancel</string><string name="main_activity_title">Enhanced Video Widgets</string><string name="add_widget">Add Widget</string><string name="my_widgets">My Widgets</string><string name="no_widgets">No widgets created yet</string><string name="widget_created">Widget created successfully</string><string name="widget_deleted">Widget deleted</string><string name="permission_storage_title">Storage Permission Required</string><string name="permission_storage_message">This app needs storage permission to access your videos.</string><string name="permission_granted">Permission granted</string><string name="permission_denied">Permission denied</string><string name="permission_video_required">Video access permission is required to select videos</string><string name="permission_settings_required">Please enable video access permission in Settings to select videos</string><string name="video_picker_error">Unable to open video picker</string><string name="error_video_processing">Error processing video</string><string name="error_widget_creation">Error creating widget</string><string name="error_file_not_found">Video file not found</string><string name="error_unsupported_format">Unsupported video format</string><string name="action_play">Play</string><string name="action_pause">Pause</string><string name="action_delete">Delete</string><string name="action_edit">Edit</string><string name="action_settings">Settings</string><string name="widget_provider_label">Enhanced Video Widget</string><string name="widget_provider_description">Display videos with play/pause controls on your home screen</string><string name="video_widgets">Video Widgets</string><string name="widget_list">Widget List</string><string name="widget_settings">Widget Settings</string><string name="upload_video">Upload a Video</string><string name="video_library_access">Access Video</string><string name="video_message">Access Video for Widget</string><string name="load_widget">Loading…</string><string name="save_widget">Save</string><string name="cancel_widget">Cancel</string><string name="create_widget">Create a Widget by long pressing the app icon</string><string name="settings">Settings</string><string name="about">About</string><string name="share">Share</string><string name="rate">Rate</string><string name="theme">Theme</string><string name="auto">Auto</string><string name="light">Light</string><string name="dark">Dark</string><string name="notifications">Notifications</string><string name="enable_notifications">Enable Notifications</string><string name="general_settings">General settings</string><string name="go_to_settings">Go to Settings</string></file><file path="D:\php\android\enhanced_video_widget_app\app\src\main\res\values\themes.xml" qualifiers=""><style name="Theme.EnhancedVideoWidget" parent="Theme.Material3.DayNight">
        
        <item name="colorPrimary">@color/purple_500</item>
        <item name="colorPrimaryVariant">@color/purple_700</item>
        <item name="colorOnPrimary">@color/white</item>
        
        <item name="colorSecondary">@color/teal_200</item>
        <item name="colorSecondaryVariant">@color/teal_700</item>
        <item name="colorOnSecondary">@color/black</item>
        
        <item name="android:statusBarColor" ns1:targetApi="l">?attr/colorPrimaryVariant</item>
        
    </style><style name="Theme.EnhancedVideoWidget.NoActionBar">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
    </style><style name="Theme.EnhancedVideoWidget.AppWidgetContainer" parent="android:Theme">
        
        <item name="appWidgetRadius">16dp</item>
        
        <item name="appWidgetInnerRadius">8dp</item>
    </style><style name="Theme.EnhancedVideoWidget.AppWidgetContainerParent" parent="@android:style/Theme.DeviceDefault">
        
        <item name="appWidgetRadius">16dp</item>
        
        <item name="appWidgetInnerRadius">8dp</item>
    </style></file><file name="backup_rules" path="D:\php\android\enhanced_video_widget_app\app\src\main\res\xml\backup_rules.xml" qualifiers="" type="xml"/><file name="data_extraction_rules" path="D:\php\android\enhanced_video_widget_app\app\src\main\res\xml\data_extraction_rules.xml" qualifiers="" type="xml"/><file name="enhanced_video_widget_info" path="D:\php\android\enhanced_video_widget_app\app\src\main\res\xml\enhanced_video_widget_info.xml" qualifiers="" type="xml"/></source><source path="D:\php\android\enhanced_video_widget_app\app\build\generated\res\rs\debug"/><source path="D:\php\android\enhanced_video_widget_app\app\build\generated\res\resValues\debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\php\android\enhanced_video_widget_app\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\php\android\enhanced_video_widget_app\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"/><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"/><mergedItems/></merger>