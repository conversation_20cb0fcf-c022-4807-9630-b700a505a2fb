<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:theme="@style/Theme.VideoWidget.AppWidgetContainer"
    android:id="@+id/widget_container"
    android:background="@android:color/transparent"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    style="@style/Widget.VideoWidget.AppWidget.Container">
    <ImageView
        android:id="@+id/imageVideoView"
        android:background="@drawable/round_outline"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:scaleType="centerCrop"
        android:layout_alignParentTop="true"
        android:layout_alignParentBottom="true"
        android:contentDescription="@string/video_message"
        android:layout_alignParentStart="true"
        android:layout_alignParentEnd="true"
        android:clipToOutline="true"/>
    <ProgressBar
        android:id="@+id/progressBar"
        android:visibility="visible"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerHorizontal="true"
        android:layout_centerVertical="true"
        style="?android:attr/progressBarStyle"/>
</RelativeLayout>
