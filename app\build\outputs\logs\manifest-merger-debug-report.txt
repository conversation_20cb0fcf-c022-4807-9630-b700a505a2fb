-- Merging decision tree log ---
provider#androidx.startup.InitializationProvider
INJECTED from D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:74:9-82:20
MERGED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:29:9-37:20
MERGED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:29:9-37:20
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\f5bce95adc2736b0739e9bbfa23aa81b\transformed\emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\f5bce95adc2736b0739e9bbfa23aa81b\transformed\emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\f79bf1fa26380065745fd6a35eb2733a\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\f79bf1fa26380065745fd6a35eb2733a\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\855f345d0f57b580ddb9a53f14817127\transformed\profileinstaller-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\855f345d0f57b580ddb9a53f14817127\transformed\profileinstaller-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\b905560b2da4be86f30e02660b710b15\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\b905560b2da4be86f30e02660b710b15\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:78:13-31
	android:authorities
		INJECTED from D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml
		ADDED from D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:76:13-68
	android:exported
		ADDED from D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:77:13-37
	android:name
		ADDED from D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:75:13-67
manifest
ADDED from D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:2:1-86:12
INJECTED from D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:2:1-86:12
INJECTED from D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:2:1-86:12
INJECTED from D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:2:1-86:12
MERGED from [androidx.databinding:viewbinding:7.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\6b7ca0c285bece8bb9139c95c6c72977\transformed\viewbinding-7.4.2\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.material:material:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c486cec99d4fccb7893779ad5d22534\transformed\material-1.9.0\AndroidManifest.xml:17:1-26:12
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\0048526f799978d53c8838b69626f826\transformed\constraintlayout-2.1.4\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\902bda61c99a6c8f4092b3028f6c7343\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\3c82f0ccdbe9412677623492bdd8999d\transformed\appcompat-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [com.github.bumptech.glide:glide:4.15.1] C:\Users\<USER>\.gradle\caches\transforms-3\9b1bf7de9954780f79afee89e8bdcac4\transformed\glide-4.15.1\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\17473142177f1aaac34c6335ae7e71fa\transformed\viewpager2-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.fragment:fragment:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\eb5331a8d2f1863f453000814746d8a5\transformed\fragment-1.6.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.fragment:fragment-ktx:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\4121efbaa3ed33d71436a7cb370ae08d\transformed\fragment-ktx-1.6.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity:1.7.2] C:\Users\<USER>\.gradle\caches\transforms-3\073717acac0594e204facd27bb3c44e8\transformed\activity-1.7.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity-ktx:1.7.2] C:\Users\<USER>\.gradle\caches\transforms-3\a27b0c67c07e629eeb6a1d94231cc685\transformed\activity-ktx-1.7.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.recyclerview:recyclerview:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\eb712f48bb001030cc1dbec315cbada8\transformed\recyclerview-1.3.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\7eaef02e3a855a9005e1f77aa6c224e7\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.work:work-runtime-ktx:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\8b5ee6cd73b44eaaab4b240e9778c086\transformed\work-runtime-ktx-2.8.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:17:1-145:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\96e99f535981952eca70dcea7436f9a8\transformed\emoji2-views-helper-1.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\f5bce95adc2736b0739e9bbfa23aa81b\transformed\emoji2-1.2.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-service:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\855c0bdad549aff5db38f1b4ab5960de\transformed\lifecycle-service-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\31082415f64834e3f2fb6ab692d703a0\transformed\lifecycle-livedata-core-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\f79bf1fa26380065745fd6a35eb2733a\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\d0f69efbdac71b06bb93f6d81f2dc192\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\053979ad14689892f71b16db0461daec\transformed\savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\61bcdcb940fbb590286c186b857db46a\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\07dd9cf1e5e9d3a0834cbd1953c0cf54\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\f0cd2140268ad88a6efad3ac05386ab8\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\4342d5d69e235ef1cfa0094d2a874798\transformed\lifecycle-livedata-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\7c0c72286a525a49da4a9878f4ce7a22\transformed\lifecycle-livedata-core-ktx-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\4031314fcdd1b8a18fac191715de1eb1\transformed\lifecycle-viewmodel-savedstate-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core-ktx:1.10.1] C:\Users\<USER>\.gradle\caches\transforms-3\be19f523bb92ffca3fffc4bed36d2bad\transformed\core-ktx-1.10.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\8f21a94e33b5c1125a773c88e58b231d\transformed\coordinatorlayout-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\e38ee089648600abdf2b1b34f81c65de\transformed\drawerlayout-1.1.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.transition:transition:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\61017ffc1d01ff413e7d898b997b69c2\transformed\transition-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\2deddba07ed6cbf9631edbc4999ddc1a\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\9c163b3c0f223d55e9867cb5b985a609\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\1b0dec12bf907a0ce71737e1bcb2c2d1\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\711082ff04b262488c989f0a20113488\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\transforms-3\6fe3e64a80094b4316c7006f0bcdcf47\transformed\core-1.10.1\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\75f4d64747eac620fc08d36ed66df32a\transformed\lifecycle-runtime-2.6.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\d552998149489dbced610e2db7d99ba7\transformed\lifecycle-viewmodel-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\ec2f61ddaa5ffc1565684145f457e530\transformed\lifecycle-runtime-ktx-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\fd08a8a6698705b387276a5bd0f9da0b\transformed\lifecycle-livedata-ktx-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\69d5c35507dafc17409097c248fe78ed\transformed\lifecycle-viewmodel-ktx-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\891ad734ff703f843ef3afba41c84481\transformed\room-runtime-2.5.0\AndroidManifest.xml:17:1-31:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\0fd9d8751b56a2d26f0d29d3b3e9f257\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\bf7e18ef8301d1d7f6bc63773afcfb9c\transformed\cardview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.github.bumptech.glide:gifdecoder:4.15.1] C:\Users\<USER>\.gradle\caches\transforms-3\aed82c230d4d4bf642201451dccf3218\transformed\gifdecoder-4.15.1\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.exifinterface:exifinterface:1.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\5c6b755c0a1391ed982bf9e93f1f45e5\transformed\exifinterface-1.3.3\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\855f345d0f57b580ddb9a53f14817127\transformed\profileinstaller-1.3.0\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\b905560b2da4be86f30e02660b710b15\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\7b2183c3cbd89865b4ee0e8a658ff57b\transformed\tracing-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\14cf0f12229a19f9854ef132476f78e1\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\b2a96e02639f78061d3b89549351b289\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\e25232853be8741a8e453b0cc5134525\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.sqlite:sqlite-framework:2.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\d64f081eae1a0c3cfc016dffdbd1daaf\transformed\sqlite-framework-2.3.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\f9b79652572637781f542df77a91d7cc\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\43551e7787504844afc94f958065b3d6\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\a0ef88f649878603ab3d2e983b9bc7b1\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.sqlite:sqlite:2.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6d4d20d5bf2d12aa2b0b796d8875f56f\transformed\sqlite-2.3.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.annotation:annotation-experimental:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\c15864de81153c1f158ef9d18de9ad3a\transformed\annotation-experimental-1.3.0\AndroidManifest.xml:17:1-22:12
INJECTED from D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:2:1-86:12
INJECTED from D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:2:1-86:12
INJECTED from D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:2:1-86:12
	package
		ADDED from D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:4:5-39
		INJECTED from D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml
		INJECTED from D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml
		ADDED from D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:2:1-86:12
		INJECTED from D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml
		ADDED from D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:2:1-86:12
		INJECTED from D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:2:11-69
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:7:5-8:38
	android:maxSdkVersion
		ADDED from D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:8:9-35
	android:name
		ADDED from D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:7:22-77
uses-permission#android.permission.READ_MEDIA_VIDEO
ADDED from D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:9:5-75
	android:name
		ADDED from D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:9:22-72
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:10:5-11:38
	android:maxSdkVersion
		ADDED from D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:11:9-35
	android:name
		ADDED from D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:10:22-78
uses-permission#android.permission.INTERNET
ADDED from D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:12:5-67
	android:name
		ADDED from D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:12:22-64
uses-permission#android.permission.WAKE_LOCK
ADDED from D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:13:5-68
MERGED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:23:5-68
MERGED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:23:5-68
	android:name
		ADDED from D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:13:22-65
uses-permission#android.permission.FOREGROUND_SERVICE
ADDED from D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:14:5-77
MERGED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:26:5-77
MERGED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:26:5-77
	android:name
		ADDED from D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:14:22-74
uses-permission#android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS
ADDED from D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:15:5-95
	android:name
		ADDED from D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:15:22-92
uses-feature#android.software.app_widgets
ADDED from D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:18:5-20:35
	android:required
		ADDED from D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:20:9-32
	android:name
		ADDED from D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:19:9-52
application
ADDED from D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:22:5-84:19
MERGED from [com.google.android.material:material:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c486cec99d4fccb7893779ad5d22534\transformed\material-1.9.0\AndroidManifest.xml:24:5-20
MERGED from [com.google.android.material:material:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c486cec99d4fccb7893779ad5d22534\transformed\material-1.9.0\AndroidManifest.xml:24:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\0048526f799978d53c8838b69626f826\transformed\constraintlayout-2.1.4\AndroidManifest.xml:9:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\0048526f799978d53c8838b69626f826\transformed\constraintlayout-2.1.4\AndroidManifest.xml:9:5-20
MERGED from [com.github.bumptech.glide:glide:4.15.1] C:\Users\<USER>\.gradle\caches\transforms-3\9b1bf7de9954780f79afee89e8bdcac4\transformed\glide-4.15.1\AndroidManifest.xml:9:5-20
MERGED from [com.github.bumptech.glide:glide:4.15.1] C:\Users\<USER>\.gradle\caches\transforms-3\9b1bf7de9954780f79afee89e8bdcac4\transformed\glide-4.15.1\AndroidManifest.xml:9:5-20
MERGED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:28:5-143:19
MERGED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:28:5-143:19
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\f5bce95adc2736b0739e9bbfa23aa81b\transformed\emoji2-1.2.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\f5bce95adc2736b0739e9bbfa23aa81b\transformed\emoji2-1.2.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\f79bf1fa26380065745fd6a35eb2733a\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\f79bf1fa26380065745fd6a35eb2733a\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\transforms-3\6fe3e64a80094b4316c7006f0bcdcf47\transformed\core-1.10.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\transforms-3\6fe3e64a80094b4316c7006f0bcdcf47\transformed\core-1.10.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\891ad734ff703f843ef3afba41c84481\transformed\room-runtime-2.5.0\AndroidManifest.xml:23:5-29:19
MERGED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\891ad734ff703f843ef3afba41c84481\transformed\room-runtime-2.5.0\AndroidManifest.xml:23:5-29:19
MERGED from [com.github.bumptech.glide:gifdecoder:4.15.1] C:\Users\<USER>\.gradle\caches\transforms-3\aed82c230d4d4bf642201451dccf3218\transformed\gifdecoder-4.15.1\AndroidManifest.xml:9:5-20
MERGED from [com.github.bumptech.glide:gifdecoder:4.15.1] C:\Users\<USER>\.gradle\caches\transforms-3\aed82c230d4d4bf642201451dccf3218\transformed\gifdecoder-4.15.1\AndroidManifest.xml:9:5-20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\855f345d0f57b580ddb9a53f14817127\transformed\profileinstaller-1.3.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\855f345d0f57b580ddb9a53f14817127\transformed\profileinstaller-1.3.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\b905560b2da4be86f30e02660b710b15\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\b905560b2da4be86f30e02660b710b15\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\b2a96e02639f78061d3b89549351b289\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\b2a96e02639f78061d3b89549351b289\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
	android:appComponentFactory
		ADDED from [androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\transforms-3\6fe3e64a80094b4316c7006f0bcdcf47\transformed\core-1.10.1\AndroidManifest.xml:28:18-86
	android:label
		ADDED from D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:27:9-41
	android:fullBackupContent
		ADDED from D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:25:9-54
	tools:targetApi
		ADDED from D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:29:9-29
	android:icon
		ADDED from D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:26:9-43
	android:allowBackup
		ADDED from D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:23:9-35
	android:theme
		ADDED from D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:28:9-57
	android:dataExtractionRules
		ADDED from D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:24:9-65
activity#com.enhanced.videowidget.ui.MainActivity
ADDED from D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:32:9-40:20
	android:exported
		ADDED from D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:34:13-36
	android:theme
		ADDED from D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:35:13-61
	android:name
		ADDED from D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:33:13-44
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:36:13-39:29
action#android.intent.action.MAIN
ADDED from D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:37:17-69
	android:name
		ADDED from D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:37:25-66
category#android.intent.category.LAUNCHER
ADDED from D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:38:17-77
	android:name
		ADDED from D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:38:27-74
activity#com.enhanced.videowidget.ui.WidgetConfigurationActivity
ADDED from D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:43:9-50:20
	android:exported
		ADDED from D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:45:13-37
	android:theme
		ADDED from D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:46:13-73
	android:name
		ADDED from D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:44:13-59
intent-filter#action:name:android.appwidget.action.APPWIDGET_CONFIGURE
ADDED from D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:47:13-49:29
action#android.appwidget.action.APPWIDGET_CONFIGURE
ADDED from D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:48:17-87
	android:name
		ADDED from D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:48:25-84
receiver#com.enhanced.videowidget.widget.EnhancedVideoWidget
ADDED from D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:53:9-65:20
	android:exported
		ADDED from D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:55:13-36
	android:name
		ADDED from D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:54:13-55
intent-filter#action:name:android.appwidget.action.APPWIDGET_UPDATE+action:name:com.enhanced.videowidget.ACTION_PLAY_PAUSE+action:name:com.enhanced.videowidget.ACTION_SETTINGS+action:name:com.enhanced.videowidget.ACTION_WIDGET_TAP
ADDED from D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:56:13-61:29
action#android.appwidget.action.APPWIDGET_UPDATE
ADDED from D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:57:17-84
	android:name
		ADDED from D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:57:25-81
action#com.enhanced.videowidget.ACTION_WIDGET_TAP
ADDED from D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:58:17-85
	android:name
		ADDED from D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:58:25-82
action#com.enhanced.videowidget.ACTION_PLAY_PAUSE
ADDED from D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:59:17-85
	android:name
		ADDED from D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:59:25-82
action#com.enhanced.videowidget.ACTION_SETTINGS
ADDED from D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:60:17-83
	android:name
		ADDED from D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:60:25-80
meta-data#android.appwidget.provider
ADDED from D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:62:13-64:70
	android:resource
		ADDED from D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:64:17-67
	android:name
		ADDED from D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:63:17-58
service#com.enhanced.videowidget.service.WidgetUpdateService
ADDED from D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:68:9-71:56
	android:exported
		ADDED from D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:70:13-37
	android:foregroundServiceType
		ADDED from D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:71:13-53
	android:name
		ADDED from D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:69:13-56
uses-sdk
INJECTED from D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml
INJECTED from D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml
MERGED from [androidx.databinding:viewbinding:7.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\6b7ca0c285bece8bb9139c95c6c72977\transformed\viewbinding-7.4.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:viewbinding:7.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\6b7ca0c285bece8bb9139c95c6c72977\transformed\viewbinding-7.4.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.material:material:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c486cec99d4fccb7893779ad5d22534\transformed\material-1.9.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.material:material:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c486cec99d4fccb7893779ad5d22534\transformed\material-1.9.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\0048526f799978d53c8838b69626f826\transformed\constraintlayout-2.1.4\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\0048526f799978d53c8838b69626f826\transformed\constraintlayout-2.1.4\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\902bda61c99a6c8f4092b3028f6c7343\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\902bda61c99a6c8f4092b3028f6c7343\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\3c82f0ccdbe9412677623492bdd8999d\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\3c82f0ccdbe9412677623492bdd8999d\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [com.github.bumptech.glide:glide:4.15.1] C:\Users\<USER>\.gradle\caches\transforms-3\9b1bf7de9954780f79afee89e8bdcac4\transformed\glide-4.15.1\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.bumptech.glide:glide:4.15.1] C:\Users\<USER>\.gradle\caches\transforms-3\9b1bf7de9954780f79afee89e8bdcac4\transformed\glide-4.15.1\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\17473142177f1aaac34c6335ae7e71fa\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\17473142177f1aaac34c6335ae7e71fa\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\eb5331a8d2f1863f453000814746d8a5\transformed\fragment-1.6.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\eb5331a8d2f1863f453000814746d8a5\transformed\fragment-1.6.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment-ktx:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\4121efbaa3ed33d71436a7cb370ae08d\transformed\fragment-ktx-1.6.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment-ktx:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\4121efbaa3ed33d71436a7cb370ae08d\transformed\fragment-ktx-1.6.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.7.2] C:\Users\<USER>\.gradle\caches\transforms-3\073717acac0594e204facd27bb3c44e8\transformed\activity-1.7.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.7.2] C:\Users\<USER>\.gradle\caches\transforms-3\073717acac0594e204facd27bb3c44e8\transformed\activity-1.7.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-ktx:1.7.2] C:\Users\<USER>\.gradle\caches\transforms-3\a27b0c67c07e629eeb6a1d94231cc685\transformed\activity-ktx-1.7.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-ktx:1.7.2] C:\Users\<USER>\.gradle\caches\transforms-3\a27b0c67c07e629eeb6a1d94231cc685\transformed\activity-ktx-1.7.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\eb712f48bb001030cc1dbec315cbada8\transformed\recyclerview-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\eb712f48bb001030cc1dbec315cbada8\transformed\recyclerview-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\7eaef02e3a855a9005e1f77aa6c224e7\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\7eaef02e3a855a9005e1f77aa6c224e7\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.work:work-runtime-ktx:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\8b5ee6cd73b44eaaab4b240e9778c086\transformed\work-runtime-ktx-2.8.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.work:work-runtime-ktx:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\8b5ee6cd73b44eaaab4b240e9778c086\transformed\work-runtime-ktx-2.8.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\96e99f535981952eca70dcea7436f9a8\transformed\emoji2-views-helper-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\96e99f535981952eca70dcea7436f9a8\transformed\emoji2-views-helper-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\f5bce95adc2736b0739e9bbfa23aa81b\transformed\emoji2-1.2.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\f5bce95adc2736b0739e9bbfa23aa81b\transformed\emoji2-1.2.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\855c0bdad549aff5db38f1b4ab5960de\transformed\lifecycle-service-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\855c0bdad549aff5db38f1b4ab5960de\transformed\lifecycle-service-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\31082415f64834e3f2fb6ab692d703a0\transformed\lifecycle-livedata-core-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\31082415f64834e3f2fb6ab692d703a0\transformed\lifecycle-livedata-core-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\f79bf1fa26380065745fd6a35eb2733a\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\f79bf1fa26380065745fd6a35eb2733a\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\d0f69efbdac71b06bb93f6d81f2dc192\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\d0f69efbdac71b06bb93f6d81f2dc192\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\053979ad14689892f71b16db0461daec\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\053979ad14689892f71b16db0461daec\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\61bcdcb940fbb590286c186b857db46a\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\61bcdcb940fbb590286c186b857db46a\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\07dd9cf1e5e9d3a0834cbd1953c0cf54\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\07dd9cf1e5e9d3a0834cbd1953c0cf54\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\f0cd2140268ad88a6efad3ac05386ab8\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\f0cd2140268ad88a6efad3ac05386ab8\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\4342d5d69e235ef1cfa0094d2a874798\transformed\lifecycle-livedata-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\4342d5d69e235ef1cfa0094d2a874798\transformed\lifecycle-livedata-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\7c0c72286a525a49da4a9878f4ce7a22\transformed\lifecycle-livedata-core-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\7c0c72286a525a49da4a9878f4ce7a22\transformed\lifecycle-livedata-core-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\4031314fcdd1b8a18fac191715de1eb1\transformed\lifecycle-viewmodel-savedstate-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\4031314fcdd1b8a18fac191715de1eb1\transformed\lifecycle-viewmodel-savedstate-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-ktx:1.10.1] C:\Users\<USER>\.gradle\caches\transforms-3\be19f523bb92ffca3fffc4bed36d2bad\transformed\core-ktx-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.10.1] C:\Users\<USER>\.gradle\caches\transforms-3\be19f523bb92ffca3fffc4bed36d2bad\transformed\core-ktx-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\8f21a94e33b5c1125a773c88e58b231d\transformed\coordinatorlayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\8f21a94e33b5c1125a773c88e58b231d\transformed\coordinatorlayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\e38ee089648600abdf2b1b34f81c65de\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\e38ee089648600abdf2b1b34f81c65de\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\61017ffc1d01ff413e7d898b997b69c2\transformed\transition-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\61017ffc1d01ff413e7d898b997b69c2\transformed\transition-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\2deddba07ed6cbf9631edbc4999ddc1a\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\2deddba07ed6cbf9631edbc4999ddc1a\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\9c163b3c0f223d55e9867cb5b985a609\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\9c163b3c0f223d55e9867cb5b985a609\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\1b0dec12bf907a0ce71737e1bcb2c2d1\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\1b0dec12bf907a0ce71737e1bcb2c2d1\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\711082ff04b262488c989f0a20113488\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\711082ff04b262488c989f0a20113488\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\transforms-3\6fe3e64a80094b4316c7006f0bcdcf47\transformed\core-1.10.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\transforms-3\6fe3e64a80094b4316c7006f0bcdcf47\transformed\core-1.10.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\75f4d64747eac620fc08d36ed66df32a\transformed\lifecycle-runtime-2.6.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\75f4d64747eac620fc08d36ed66df32a\transformed\lifecycle-runtime-2.6.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\d552998149489dbced610e2db7d99ba7\transformed\lifecycle-viewmodel-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\d552998149489dbced610e2db7d99ba7\transformed\lifecycle-viewmodel-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\ec2f61ddaa5ffc1565684145f457e530\transformed\lifecycle-runtime-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\ec2f61ddaa5ffc1565684145f457e530\transformed\lifecycle-runtime-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\fd08a8a6698705b387276a5bd0f9da0b\transformed\lifecycle-livedata-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\fd08a8a6698705b387276a5bd0f9da0b\transformed\lifecycle-livedata-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\69d5c35507dafc17409097c248fe78ed\transformed\lifecycle-viewmodel-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\69d5c35507dafc17409097c248fe78ed\transformed\lifecycle-viewmodel-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\891ad734ff703f843ef3afba41c84481\transformed\room-runtime-2.5.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\891ad734ff703f843ef3afba41c84481\transformed\room-runtime-2.5.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\0fd9d8751b56a2d26f0d29d3b3e9f257\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\0fd9d8751b56a2d26f0d29d3b3e9f257\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\bf7e18ef8301d1d7f6bc63773afcfb9c\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\bf7e18ef8301d1d7f6bc63773afcfb9c\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.github.bumptech.glide:gifdecoder:4.15.1] C:\Users\<USER>\.gradle\caches\transforms-3\aed82c230d4d4bf642201451dccf3218\transformed\gifdecoder-4.15.1\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.bumptech.glide:gifdecoder:4.15.1] C:\Users\<USER>\.gradle\caches\transforms-3\aed82c230d4d4bf642201451dccf3218\transformed\gifdecoder-4.15.1\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.exifinterface:exifinterface:1.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\5c6b755c0a1391ed982bf9e93f1f45e5\transformed\exifinterface-1.3.3\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\5c6b755c0a1391ed982bf9e93f1f45e5\transformed\exifinterface-1.3.3\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\855f345d0f57b580ddb9a53f14817127\transformed\profileinstaller-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\855f345d0f57b580ddb9a53f14817127\transformed\profileinstaller-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\b905560b2da4be86f30e02660b710b15\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\b905560b2da4be86f30e02660b710b15\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\7b2183c3cbd89865b4ee0e8a658ff57b\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\7b2183c3cbd89865b4ee0e8a658ff57b\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\14cf0f12229a19f9854ef132476f78e1\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\14cf0f12229a19f9854ef132476f78e1\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\b2a96e02639f78061d3b89549351b289\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\b2a96e02639f78061d3b89549351b289\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\e25232853be8741a8e453b0cc5134525\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\e25232853be8741a8e453b0cc5134525\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\d64f081eae1a0c3cfc016dffdbd1daaf\transformed\sqlite-framework-2.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\d64f081eae1a0c3cfc016dffdbd1daaf\transformed\sqlite-framework-2.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\f9b79652572637781f542df77a91d7cc\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\f9b79652572637781f542df77a91d7cc\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\43551e7787504844afc94f958065b3d6\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\43551e7787504844afc94f958065b3d6\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\a0ef88f649878603ab3d2e983b9bc7b1\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\a0ef88f649878603ab3d2e983b9bc7b1\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite:2.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6d4d20d5bf2d12aa2b0b796d8875f56f\transformed\sqlite-2.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite:2.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6d4d20d5bf2d12aa2b0b796d8875f56f\transformed\sqlite-2.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\c15864de81153c1f158ef9d18de9ad3a\transformed\annotation-experimental-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\c15864de81153c1f158ef9d18de9ad3a\transformed\annotation-experimental-1.3.0\AndroidManifest.xml:20:5-44
INJECTED from D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml
INJECTED from D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml
	android:targetSdkVersion
		INJECTED from D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml
		ADDED from D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml
		INJECTED from D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml
		ADDED from D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml
		INJECTED from D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml
meta-data#androidx.work.WorkManagerInitializer
ADDED from D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:79:13-81:52
MERGED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:34:13-36:52
MERGED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:34:13-36:52
	android:value
		ADDED from D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:81:17-49
	android:name
		ADDED from D:\php\android\enhanced_video_widget_app\app\src\main\AndroidManifest.xml:80:17-68
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:24:5-79
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:24:22-76
uses-permission#android.permission.RECEIVE_BOOT_COMPLETED
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:25:5-81
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:25:22-78
service#androidx.work.impl.background.systemalarm.SystemAlarmService
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:39:9-45:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:42:13-72
	android:exported
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:43:13-37
	tools:ignore
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:44:13-60
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:45:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:41:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:40:13-88
service#androidx.work.impl.background.systemjob.SystemJobService
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:46:9-52:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:49:13-70
	android:exported
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:50:13-36
	android:permission
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:51:13-69
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:52:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:48:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:47:13-84
service#androidx.work.impl.foreground.SystemForegroundService
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:53:9-59:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:56:13-77
	android:exported
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:57:13-37
	tools:ignore
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:58:13-60
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:59:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:55:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:54:13-81
receiver#androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:61:9-66:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:64:13-35
	android:exported
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:65:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:66:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:63:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:62:13-88
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:67:9-77:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:70:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:71:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:72:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:69:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:68:13-106
intent-filter#action:name:android.intent.action.ACTION_POWER_CONNECTED+action:name:android.intent.action.ACTION_POWER_DISCONNECTED
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:73:13-76:29
action#android.intent.action.ACTION_POWER_CONNECTED
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:74:17-87
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:74:25-84
action#android.intent.action.ACTION_POWER_DISCONNECTED
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:75:17-90
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:75:25-87
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:78:9-88:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:81:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:82:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:83:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:80:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:79:13-104
intent-filter#action:name:android.intent.action.BATTERY_LOW+action:name:android.intent.action.BATTERY_OKAY
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:84:13-87:29
action#android.intent.action.BATTERY_OKAY
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:85:17-77
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:85:25-74
action#android.intent.action.BATTERY_LOW
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:86:17-76
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:86:25-73
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:89:9-99:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:92:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:93:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:94:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:91:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:90:13-104
intent-filter#action:name:android.intent.action.DEVICE_STORAGE_LOW+action:name:android.intent.action.DEVICE_STORAGE_OK
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:95:13-98:29
action#android.intent.action.DEVICE_STORAGE_LOW
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:96:17-83
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:96:25-80
action#android.intent.action.DEVICE_STORAGE_OK
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:97:17-82
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:97:25-79
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:100:9-109:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:103:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:104:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:105:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:102:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:101:13-103
intent-filter#action:name:android.net.conn.CONNECTIVITY_CHANGE
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:106:13-108:29
action#android.net.conn.CONNECTIVITY_CHANGE
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:107:17-79
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:107:25-76
receiver#androidx.work.impl.background.systemalarm.RescheduleReceiver
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:110:9-121:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:113:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:114:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:115:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:112:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:111:13-88
intent-filter#action:name:android.intent.action.BOOT_COMPLETED+action:name:android.intent.action.TIMEZONE_CHANGED+action:name:android.intent.action.TIME_SET
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:116:13-120:29
action#android.intent.action.BOOT_COMPLETED
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:117:17-79
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:117:25-76
action#android.intent.action.TIME_SET
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:118:17-73
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:118:25-70
action#android.intent.action.TIMEZONE_CHANGED
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:119:17-81
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:119:25-78
receiver#androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:122:9-131:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:125:13-72
	android:exported
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:126:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:127:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:124:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:123:13-99
intent-filter#action:name:androidx.work.impl.background.systemalarm.UpdateProxies
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:128:13-130:29
action#androidx.work.impl.background.systemalarm.UpdateProxies
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:129:17-98
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:129:25-95
receiver#androidx.work.impl.diagnostics.DiagnosticsReceiver
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:132:9-142:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:135:13-35
	android:exported
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:136:13-36
	android:permission
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:137:13-57
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:138:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:134:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:133:13-78
intent-filter#action:name:androidx.work.diagnostics.REQUEST_DIAGNOSTICS
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:139:13-141:29
action#androidx.work.diagnostics.REQUEST_DIAGNOSTICS
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:140:17-88
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\c791753e8b0cfc751cd89121d7d960c6\transformed\work-runtime-2.8.1\AndroidManifest.xml:140:25-85
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\f5bce95adc2736b0739e9bbfa23aa81b\transformed\emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\f5bce95adc2736b0739e9bbfa23aa81b\transformed\emoji2-1.2.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\f5bce95adc2736b0739e9bbfa23aa81b\transformed\emoji2-1.2.0\AndroidManifest.xml:30:17-75
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\f79bf1fa26380065745fd6a35eb2733a\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\f79bf1fa26380065745fd6a35eb2733a\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\f79bf1fa26380065745fd6a35eb2733a\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:30:17-78
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\transforms-3\6fe3e64a80094b4316c7006f0bcdcf47\transformed\core-1.10.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\transforms-3\6fe3e64a80094b4316c7006f0bcdcf47\transformed\core-1.10.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\transforms-3\6fe3e64a80094b4316c7006f0bcdcf47\transformed\core-1.10.1\AndroidManifest.xml:23:9-81
permission#com.enhanced.videowidget.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\transforms-3\6fe3e64a80094b4316c7006f0bcdcf47\transformed\core-1.10.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\transforms-3\6fe3e64a80094b4316c7006f0bcdcf47\transformed\core-1.10.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\transforms-3\6fe3e64a80094b4316c7006f0bcdcf47\transformed\core-1.10.1\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\transforms-3\6fe3e64a80094b4316c7006f0bcdcf47\transformed\core-1.10.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\transforms-3\6fe3e64a80094b4316c7006f0bcdcf47\transformed\core-1.10.1\AndroidManifest.xml:26:22-94
uses-permission#com.enhanced.videowidget.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\transforms-3\6fe3e64a80094b4316c7006f0bcdcf47\transformed\core-1.10.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\transforms-3\6fe3e64a80094b4316c7006f0bcdcf47\transformed\core-1.10.1\AndroidManifest.xml:26:22-94
service#androidx.room.MultiInstanceInvalidationService
ADDED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\891ad734ff703f843ef3afba41c84481\transformed\room-runtime-2.5.0\AndroidManifest.xml:24:9-28:63
	android:exported
		ADDED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\891ad734ff703f843ef3afba41c84481\transformed\room-runtime-2.5.0\AndroidManifest.xml:27:13-37
	tools:ignore
		ADDED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\891ad734ff703f843ef3afba41c84481\transformed\room-runtime-2.5.0\AndroidManifest.xml:28:13-60
	android:directBootAware
		ADDED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\891ad734ff703f843ef3afba41c84481\transformed\room-runtime-2.5.0\AndroidManifest.xml:26:13-43
	android:name
		ADDED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\891ad734ff703f843ef3afba41c84481\transformed\room-runtime-2.5.0\AndroidManifest.xml:25:13-74
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\855f345d0f57b580ddb9a53f14817127\transformed\profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\855f345d0f57b580ddb9a53f14817127\transformed\profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\855f345d0f57b580ddb9a53f14817127\transformed\profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\855f345d0f57b580ddb9a53f14817127\transformed\profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\855f345d0f57b580ddb9a53f14817127\transformed\profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\855f345d0f57b580ddb9a53f14817127\transformed\profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\855f345d0f57b580ddb9a53f14817127\transformed\profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\855f345d0f57b580ddb9a53f14817127\transformed\profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\855f345d0f57b580ddb9a53f14817127\transformed\profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\855f345d0f57b580ddb9a53f14817127\transformed\profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\855f345d0f57b580ddb9a53f14817127\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\855f345d0f57b580ddb9a53f14817127\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\855f345d0f57b580ddb9a53f14817127\transformed\profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\855f345d0f57b580ddb9a53f14817127\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\855f345d0f57b580ddb9a53f14817127\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\855f345d0f57b580ddb9a53f14817127\transformed\profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\855f345d0f57b580ddb9a53f14817127\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\855f345d0f57b580ddb9a53f14817127\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\855f345d0f57b580ddb9a53f14817127\transformed\profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\855f345d0f57b580ddb9a53f14817127\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\855f345d0f57b580ddb9a53f14817127\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
