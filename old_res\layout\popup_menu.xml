<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="vertical"
    android:background="@drawable/popup_background"
    android:padding="0dp"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:elevation="16dp">
    <TextView
        android:textSize="16sp"
        android:id="@+id/action_manage_subscription"
        android:background="?attr/selectableItemBackground"
        android:padding="16dp"
        android:focusable="true"
        android:clickable="true"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/manage_subscription"/>
    <TextView
        android:textSize="16sp"
        android:id="@+id/action_share"
        android:background="?attr/selectableItemBackground"
        android:padding="16dp"
        android:focusable="true"
        android:clickable="true"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/share"/>
    <TextView
        android:textSize="16sp"
        android:id="@+id/action_rate"
        android:background="?attr/selectableItemBackground"
        android:padding="16dp"
        android:focusable="true"
        android:clickable="true"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/rate"/>
    <TextView
        android:textSize="16sp"
        android:id="@+id/action_notifications"
        android:background="?attr/selectableItemBackground"
        android:padding="16dp"
        android:focusable="true"
        android:clickable="true"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/notifications"/>
    <TextView
        android:textSize="16sp"
        android:id="@+id/action_settings"
        android:background="?attr/selectableItemBackground"
        android:padding="16dp"
        android:focusable="true"
        android:clickable="true"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/settings"/>
    <TextView
        android:textSize="16sp"
        android:id="@+id/spacer"
        android:paddingLeft="16dp"
        android:paddingRight="16dp"
        android:layout_width="wrap_content"
        android:layout_height="0dp"
        android:text="@string/manage_subscription"
        android:paddingHorizontal="16dp"/>
</LinearLayout>
