package com.enhanced.videowidget.ui;

import java.lang.System;

/**
 * Configuration activity for setting up new video widgets
 */
@kotlin.Metadata(mv = {1, 8, 0}, k = 1, d1 = {"\u0000L\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\u0011\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0002\b\u0007\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0007\u0018\u0000 \"2\u00020\u0001:\u0001\"B\u0005\u00a2\u0006\u0002\u0010\u0002J\b\u0010\u0011\u001a\u00020\u0012H\u0002J\b\u0010\u0013\u001a\u00020\u0012H\u0002J\u0012\u0010\u0014\u001a\u0004\u0018\u00010\b2\u0006\u0010\u0015\u001a\u00020\rH\u0002J\u0010\u0010\u0016\u001a\u00020\u00122\u0006\u0010\u0017\u001a\u00020\rH\u0002J\u0012\u0010\u0018\u001a\u00020\u00122\b\u0010\u0019\u001a\u0004\u0018\u00010\u001aH\u0014J\b\u0010\u001b\u001a\u00020\u001cH\u0016J\b\u0010\u001d\u001a\u00020\u0012H\u0002J\b\u0010\u001e\u001a\u00020\u0012H\u0002J\b\u0010\u001f\u001a\u00020\u0012H\u0002J\u0010\u0010 \u001a\u00020\u00122\u0006\u0010!\u001a\u00020\bH\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u001a\u0010\u0005\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\b0\u00070\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\nX\u0082.\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u000b\u001a\u0004\u0018\u00010\bX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010\f\u001a\u0004\u0018\u00010\rX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u001c\u0010\u000e\u001a\u0010\u0012\f\u0012\n \u0010*\u0004\u0018\u00010\u000f0\u000f0\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006#"}, d2 = {"Lcom/enhanced/videowidget/ui/WidgetConfigurationActivity;", "Landroidx/appcompat/app/AppCompatActivity;", "()V", "appWidgetId", "", "permissionLauncher", "Landroidx/activity/result/ActivityResultLauncher;", "", "", "repository", "Lcom/enhanced/videowidget/data/repository/WidgetRepository;", "selectedVideoPath", "selectedVideoUri", "Landroid/net/Uri;", "videoPickerLauncher", "Landroid/content/Intent;", "kotlin.jvm.PlatformType", "checkPermissionsAndOpenPicker", "", "createWidget", "getVideoPath", "uri", "handleVideoSelection", "videoUri", "onCreate", "savedInstanceState", "Landroid/os/Bundle;", "onSupportNavigateUp", "", "openVideoPicker", "setupRepository", "setupUI", "showError", "message", "Companion", "app_debug"})
public final class WidgetConfigurationActivity extends androidx.appcompat.app.AppCompatActivity {
    @org.jetbrains.annotations.NotNull
    public static final com.enhanced.videowidget.ui.WidgetConfigurationActivity.Companion Companion = null;
    private static final java.lang.String TAG = "WidgetConfiguration";
    private com.enhanced.videowidget.data.repository.WidgetRepository repository;
    private int appWidgetId = android.appwidget.AppWidgetManager.INVALID_APPWIDGET_ID;
    private android.net.Uri selectedVideoUri;
    private java.lang.String selectedVideoPath;
    private final androidx.activity.result.ActivityResultLauncher<android.content.Intent> videoPickerLauncher = null;
    private final androidx.activity.result.ActivityResultLauncher<java.lang.String[]> permissionLauncher = null;
    
    public WidgetConfigurationActivity() {
        super();
    }
    
    @java.lang.Override
    protected void onCreate(@org.jetbrains.annotations.Nullable
    android.os.Bundle savedInstanceState) {
    }
    
    private final void setupRepository() {
    }
    
    private final void setupUI() {
    }
    
    private final void checkPermissionsAndOpenPicker() {
    }
    
    private final void openVideoPicker() {
    }
    
    private final void handleVideoSelection(android.net.Uri videoUri) {
    }
    
    private final java.lang.String getVideoPath(android.net.Uri uri) {
        return null;
    }
    
    private final void createWidget() {
    }
    
    private final void showError(java.lang.String message) {
    }
    
    @java.lang.Override
    public boolean onSupportNavigateUp() {
        return false;
    }
    
    @kotlin.Metadata(mv = {1, 8, 0}, k = 1, d1 = {"\u0000\u0012\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0005"}, d2 = {"Lcom/enhanced/videowidget/ui/WidgetConfigurationActivity$Companion;", "", "()V", "TAG", "", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
    }
}