<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@color/background"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:backgroundTint="@color/background">
    <com.google.android.material.button.MaterialButton
        android:id="@+id/backBtn"
        android:background="?attr/selectableItemBackgroundBorderless"
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:contentDescription="Back"
        android:layout_marginStart="12dp"
        android:layout_marginEnd="6dp"
        app:icon="@drawable/ic_back"
        app:iconGravity="textStart"
        app:iconPadding="-4dp"
        app:iconSize="20dp"
        app:iconTint="@color/textColor"
        app:layout_constraintBottom_toBottomOf="@+id/headerText"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/headerText"
        style="@style/Widget.MaterialComponents.Button.Icon"/>
    <TextView
        android:textSize="32sp"
        android:textStyle="bold"
        android:textColor="@color/textColor"
        android:id="@+id/headerText"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="44dp"
        android:text="Settings"
        android:layout_marginStart="12dp"
        app:layout_constraintStart_toEndOf="@+id/backBtn"
        app:layout_constraintTop_toTopOf="parent"/>
    <LinearLayout
        android:orientation="vertical"
        android:id="@+id/generalSettings"
        android:padding="16dp"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/headerText">
        <TextView
            android:textSize="18sp"
            android:textStyle="bold"
            android:textColor="@color/textColor"
            android:id="@+id/generalSettingsHeader"
            android:paddingTop="16dp"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/general_settings"/>
        <LinearLayout
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:id="@+id/themeSettings"
            android:paddingTop="8dp"
            android:paddingBottom="8dp"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingVertical="8dp">
            <TextView
                android:textSize="16sp"
                android:textColor="@color/textColor"
                android:id="@+id/themeText"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:text="@string/theme"
                android:layout_weight="1"/>
            <RadioGroup
                android:orientation="horizontal"
                android:id="@+id/themeOptions"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content">
                <RadioButton
                    android:textColor="@color/textColor"
                    android:id="@+id/themeLight"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/light"
                    android:buttonTint="@color/radio_button_color_selector"/>
                <RadioButton
                    android:textColor="@color/textColor"
                    android:id="@+id/themeDark"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/dark"
                    android:buttonTint="@color/radio_button_color_selector"/>
                <RadioButton
                    android:textColor="@color/textColor"
                    android:id="@+id/themeAuto"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/auto"
                    android:buttonTint="@color/radio_button_color_selector"/>
            </RadioGroup>
        </LinearLayout>
        <LinearLayout
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:id="@+id/notificationSettings"
            android:paddingTop="8dp"
            android:paddingBottom="8dp"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingVertical="8dp">
            <TextView
                android:textSize="16sp"
                android:textColor="@color/textColor"
                android:id="@+id/notificationsText"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:text="@string/enable_notifications"
                android:layout_weight="1"/>
            <androidx.appcompat.widget.SwitchCompat
                android:id="@+id/notificationSwitch"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text=""
                app:thumbTint="@color/blue"
                app:trackTint="@color/blue_light"/>
        </LinearLayout>
    </LinearLayout>
    <LinearLayout
        android:orientation="vertical"
        android:id="@+id/legalSettings"
        android:padding="16dp"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/generalSettings">
        <TextView
            android:textSize="18sp"
            android:textStyle="bold"
            android:textColor="@color/textColor"
            android:id="@+id/legalHeader"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/legal"/>
        <LinearLayout
            android:orientation="horizontal"
            android:paddingTop="8dp"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">
            <ImageView
                android:id="@+id/privacyIcon"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:src="@drawable/ic_link"
                android:layout_marginEnd="8dp"
                app:tint="@color/blue"/>
            <TextView
                android:textSize="16sp"
                android:textColor="@color/blue"
                android:id="@+id/privacyPolicy"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:text="@string/privacy_policy"
                android:layout_weight="1"/>
        </LinearLayout>
        <LinearLayout
            android:orientation="horizontal"
            android:paddingTop="8dp"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">
            <ImageView
                android:id="@+id/tosIcon"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:src="@drawable/ic_link"
                android:layout_marginEnd="8dp"
                app:tint="@color/blue"/>
            <TextView
                android:textSize="16sp"
                android:textColor="@color/blue"
                android:id="@+id/tos"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:text="@string/terms_of_service"
                android:layout_weight="1"/>
        </LinearLayout>
    </LinearLayout>
    <LinearLayout
        android:orientation="vertical"
        android:id="@+id/aboutSettings"
        android:padding="16dp"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/legalSettings">
        <TextView
            android:textSize="18sp"
            android:textStyle="bold"
            android:textColor="@color/textColor"
            android:id="@+id/aboutHeader"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/about"/>
        <TextView
            android:textSize="16sp"
            android:textColor="@color/textColor"
            android:id="@+id/versionInfo"
            android:paddingTop="8dp"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Version 1.0.0"/>
    </LinearLayout>
</androidx.constraintlayout.widget.ConstraintLayout>
