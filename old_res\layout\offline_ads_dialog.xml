<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="vertical"
    android:background="@drawable/offline_dialog_background"
    android:paddingLeft="24dp"
    android:paddingTop="40dp"
    android:paddingRight="24dp"
    android:paddingBottom="40dp"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:filterTouchesWhenObscured="true"
    android:paddingHorizontal="24dp"
    android:paddingVertical="40dp">
    <ImageView
        android:layout_gravity="center"
        android:id="@+id/offline_dialog_image"
        android:layout_width="42dp"
        android:layout_height="42dp"
        android:src="@drawable/offline_dialog_default_icon_42dp"
        android:scaleType="centerInside"
        android:contentDescription="@string/offline_dialog_image_description"/>
    <LinearLayout
        android:orientation="vertical"
        android:paddingTop="14dp"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">
        <TextView
            android:textColor="@android:color/black"
            android:gravity="center"
            android:id="@+id/offline_dialog_text"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/offline_dialog_text"/>
        <TextView
            android:textColor="@android:color/darker_gray"
            android:gravity="center"
            android:id="@+id/offline_dialog_advertiser_name"
            android:paddingTop="12dp"
            android:visibility="gone"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text=""/>
    </LinearLayout>
</LinearLayout>
