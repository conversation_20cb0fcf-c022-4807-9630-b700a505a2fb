package com.enhanced.videowidget.utils

import android.graphics.Bitmap
import android.media.MediaMetadataRetriever
import android.util.Log
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.File
import java.io.FileOutputStream

/**
 * Utility class for extracting frames from video files
 */
class VideoFrameExtractor {
    
    companion object {
        private const val TAG = "VideoFrameExtractor"
        private const val DEFAULT_MAX_FRAMES = 100
        private const val DEFAULT_TARGET_SIZE = 300
    }
    
    data class FrameInfo(
        val frameIndex: Int,
        val timestamp: Long,
        val filePath: String
    )
    
    /**
     * Extract frames from a video file
     */
    suspend fun extractFrames(
        videoPath: String,
        outputDirectory: String,
        maxFrames: Int = DEFAULT_MAX_FRAMES,
        targetWidth: Int = DEFAULT_TARGET_SIZE,
        targetHeight: Int = DEFAULT_TARGET_SIZE
    ): List<FrameInfo> = withContext(Dispatchers.IO) {
        
        val frameInfoList = mutableListOf<FrameInfo>()
        val retriever = MediaMetadataRetriever()
        
        try {
            retriever.setDataSource(videoPath)
            
            // Get video duration and frame rate
            val durationString = retriever.extractMetadata(MediaMetadataRetriever.METADATA_KEY_DURATION)
            val duration = durationString?.toLongOrNull() ?: 0L
            
            if (duration <= 0) {
                Log.e(TAG, "Invalid video duration: $duration")
                return@withContext emptyList()
            }
            
            // Calculate frame interval
            val frameInterval = duration / maxFrames
            
            Log.d(TAG, "Extracting frames from video: $videoPath")
            Log.d(TAG, "Duration: ${duration}ms, Max frames: $maxFrames, Interval: ${frameInterval}ms")
            
            // Create output directory
            val outputDir = File(outputDirectory)
            if (!outputDir.exists()) {
                outputDir.mkdirs()
            }
            
            // Extract frames
            for (i in 0 until maxFrames) {
                val timestamp = i * frameInterval * 1000 // Convert to microseconds
                
                try {
                    val bitmap = retriever.getFrameAtTime(timestamp, MediaMetadataRetriever.OPTION_CLOSEST_SYNC)
                    
                    if (bitmap != null) {
                        // Resize bitmap if needed
                        val resizedBitmap = resizeBitmap(bitmap, targetWidth, targetHeight)
                        
                        // Save frame to file
                        val frameFile = File(outputDir, "frame_$i.png")
                        saveFrameToFile(resizedBitmap, frameFile.absolutePath)
                        
                        frameInfoList.add(
                            FrameInfo(
                                frameIndex = i,
                                timestamp = timestamp / 1000, // Convert back to milliseconds
                                filePath = frameFile.absolutePath
                            )
                        )
                        
                        // Clean up bitmaps
                        if (resizedBitmap != bitmap) {
                            bitmap.recycle()
                        }
                        resizedBitmap.recycle()
                        
                        Log.v(TAG, "Extracted frame $i at timestamp ${timestamp / 1000}ms")
                    } else {
                        Log.w(TAG, "Failed to extract frame at timestamp ${timestamp / 1000}ms")
                    }
                } catch (e: Exception) {
                    Log.e(TAG, "Error extracting frame $i", e)
                }
            }
            
            Log.d(TAG, "Successfully extracted ${frameInfoList.size} frames")
            
        } catch (e: Exception) {
            Log.e(TAG, "Error extracting frames from video: $videoPath", e)
        } finally {
            try {
                retriever.release()
            } catch (e: Exception) {
                Log.e(TAG, "Error releasing MediaMetadataRetriever", e)
            }
        }
        
        frameInfoList
    }
    
    /**
     * Extract a single frame at a specific timestamp
     */
    suspend fun extractFrameAtTime(
        videoPath: String,
        timestampMs: Long,
        targetWidth: Int = DEFAULT_TARGET_SIZE,
        targetHeight: Int = DEFAULT_TARGET_SIZE
    ): Bitmap? = withContext(Dispatchers.IO) {
        
        val retriever = MediaMetadataRetriever()
        
        try {
            retriever.setDataSource(videoPath)
            
            val timestampUs = timestampMs * 1000 // Convert to microseconds
            val bitmap = retriever.getFrameAtTime(timestampUs, MediaMetadataRetriever.OPTION_CLOSEST_SYNC)
            
            return@withContext bitmap?.let { resizeBitmap(it, targetWidth, targetHeight) }
            
        } catch (e: Exception) {
            Log.e(TAG, "Error extracting frame at time $timestampMs", e)
            return@withContext null
        } finally {
            try {
                retriever.release()
            } catch (e: Exception) {
                Log.e(TAG, "Error releasing MediaMetadataRetriever", e)
            }
        }
    }
    
    /**
     * Get video metadata
     */
    suspend fun getVideoMetadata(videoPath: String): VideoMetadata? = withContext(Dispatchers.IO) {
        val retriever = MediaMetadataRetriever()
        
        try {
            retriever.setDataSource(videoPath)
            
            val duration = retriever.extractMetadata(MediaMetadataRetriever.METADATA_KEY_DURATION)?.toLongOrNull() ?: 0L
            val width = retriever.extractMetadata(MediaMetadataRetriever.METADATA_KEY_VIDEO_WIDTH)?.toIntOrNull() ?: 0
            val height = retriever.extractMetadata(MediaMetadataRetriever.METADATA_KEY_VIDEO_HEIGHT)?.toIntOrNull() ?: 0
            val frameRate = retriever.extractMetadata(MediaMetadataRetriever.METADATA_KEY_CAPTURE_FRAMERATE)?.toFloatOrNull() ?: 30f
            
            return@withContext VideoMetadata(
                duration = duration,
                width = width,
                height = height,
                frameRate = frameRate
            )
            
        } catch (e: Exception) {
            Log.e(TAG, "Error getting video metadata", e)
            return@withContext null
        } finally {
            try {
                retriever.release()
            } catch (e: Exception) {
                Log.e(TAG, "Error releasing MediaMetadataRetriever", e)
            }
        }
    }
    
    private fun resizeBitmap(bitmap: Bitmap, targetWidth: Int, targetHeight: Int): Bitmap {
        return if (bitmap.width != targetWidth || bitmap.height != targetHeight) {
            Bitmap.createScaledBitmap(bitmap, targetWidth, targetHeight, true)
        } else {
            bitmap
        }
    }
    
    private fun saveFrameToFile(bitmap: Bitmap, filePath: String) {
        FileOutputStream(filePath).use { out ->
            bitmap.compress(Bitmap.CompressFormat.PNG, 90, out)
        }
    }
    
    data class VideoMetadata(
        val duration: Long,
        val width: Int,
        val height: Int,
        val frameRate: Float
    )
}
