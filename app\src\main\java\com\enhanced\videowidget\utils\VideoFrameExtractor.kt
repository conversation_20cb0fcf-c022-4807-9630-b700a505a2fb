package com.enhanced.videowidget.utils

import android.graphics.Bitmap
import android.media.MediaMetadataRetriever
import android.util.Log
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.File
import java.io.FileOutputStream

/**
 * Utility class for extracting frames from video files
 */
class VideoFrameExtractor {
    
    companion object {
        private const val DEFAULT_MAX_FRAMES = 100
        private const val DEFAULT_TARGET_SIZE = 300
    }
    
    data class FrameInfo(
        val frameIndex: Int,
        val timestamp: Long,
        val filePath: String
    )
    
    /**
     * Extract frames from a video file
     */
    suspend fun extractFrames(
        videoPath: String,
        outputDirectory: String,
        maxFrames: Int = DEFAULT_MAX_FRAMES,
        targetWidth: Int = DEFAULT_TARGET_SIZE,
        targetHeight: Int = DEFAULT_TARGET_SIZE
    ): List<FrameInfo> = withContext(Dispatchers.IO) {

        AppLogger.i(AppLogger.TAG_FRAMES, "=== STARTING FRAME EXTRACTION ===")
        AppLogger.d(AppLogger.TAG_FRAMES, "Video path: $videoPath")
        AppLogger.d(AppLogger.TAG_FRAMES, "Output directory: $outputDirectory")
        AppLogger.d(AppLogger.TAG_FRAMES, "Max frames: $maxFrames, Target size: ${targetWidth}x${targetHeight}")

        val frameInfoList = mutableListOf<FrameInfo>()
        val retriever = MediaMetadataRetriever()

        try {
            AppLogger.d(AppLogger.TAG_FRAMES, "Setting data source...")
            retriever.setDataSource(videoPath)
            AppLogger.d(AppLogger.TAG_FRAMES, "Data source set successfully")

            // Get video duration and frame rate
            val durationString = retriever.extractMetadata(MediaMetadataRetriever.METADATA_KEY_DURATION)
            val duration = durationString?.toLongOrNull() ?: 0L

            AppLogger.d(AppLogger.TAG_FRAMES, "Raw duration string: '$durationString'")
            AppLogger.d(AppLogger.TAG_FRAMES, "Parsed duration: ${duration}ms")

            if (duration <= 0) {
                AppLogger.e(AppLogger.TAG_FRAMES, "Invalid video duration: $duration")
                return@withContext emptyList()
            }

            // Calculate frame interval
            val frameInterval = duration / maxFrames

            AppLogger.i(AppLogger.TAG_FRAMES, "Video duration: ${duration}ms (${duration/1000}s)")
            AppLogger.i(AppLogger.TAG_FRAMES, "Frame interval: ${frameInterval}ms")
            AppLogger.i(AppLogger.TAG_FRAMES, "Will extract $maxFrames frames")
            
            // Create output directory
            val outputDir = File(outputDirectory)
            AppLogger.d(AppLogger.TAG_FRAMES, "Output directory: ${outputDir.absolutePath}")
            if (!outputDir.exists()) {
                val created = outputDir.mkdirs()
                AppLogger.d(AppLogger.TAG_FRAMES, "Created output directory: $created")
            } else {
                AppLogger.d(AppLogger.TAG_FRAMES, "Output directory already exists")
            }

            // Extract frames
            AppLogger.i(AppLogger.TAG_FRAMES, "Starting frame extraction loop...")
            for (i in 0 until maxFrames) {
                val timestamp = i * frameInterval * 1000 // Convert to microseconds
                AppLogger.v(AppLogger.TAG_FRAMES, "Extracting frame $i at timestamp ${timestamp}μs (${timestamp/1000}ms)")

                try {
                    val bitmap = retriever.getFrameAtTime(timestamp, MediaMetadataRetriever.OPTION_CLOSEST_SYNC)

                    if (bitmap != null) {
                        AppLogger.v(AppLogger.TAG_FRAMES, "Frame $i extracted successfully - Size: ${bitmap.width}x${bitmap.height}")

                        // Resize bitmap if needed
                        val resizedBitmap = resizeBitmap(bitmap, targetWidth, targetHeight)
                        AppLogger.v(AppLogger.TAG_FRAMES, "Frame $i resized to: ${resizedBitmap.width}x${resizedBitmap.height}")

                        // Save frame to file
                        val frameFile = File(outputDir, "frame_$i.png")
                        val saved = saveFrameToFile(resizedBitmap, frameFile.absolutePath)
                        AppLogger.v(AppLogger.TAG_FRAMES, "Frame $i saved to file: $saved - ${frameFile.absolutePath}")

                        if (saved) {
                            frameInfoList.add(
                                FrameInfo(
                                    frameIndex = i,
                                    timestamp = timestamp / 1000, // Convert back to milliseconds
                                    filePath = frameFile.absolutePath
                                )
                            )
                            AppLogger.logFrameExtraction(i, maxFrames, true)
                        } else {
                            AppLogger.w(AppLogger.TAG_FRAMES, "Failed to save frame $i to file")
                        }

                        // Clean up bitmaps
                        if (resizedBitmap != bitmap) {
                            bitmap.recycle()
                        }
                        resizedBitmap.recycle()

                    } else {
                        AppLogger.w(AppLogger.TAG_FRAMES, "Failed to extract frame $i at timestamp ${timestamp / 1000}ms - bitmap is null")
                        AppLogger.logFrameExtraction(i, maxFrames, false)
                    }
                } catch (e: Exception) {
                    AppLogger.e(AppLogger.TAG_FRAMES, "Error extracting frame $i at timestamp ${timestamp / 1000}ms", e)
                    AppLogger.logFrameExtraction(i, maxFrames, false)
                }
            }

            AppLogger.i(AppLogger.TAG_FRAMES, "Frame extraction completed - Successfully extracted ${frameInfoList.size}/$maxFrames frames")

        } catch (e: Exception) {
            AppLogger.e(AppLogger.TAG_FRAMES, "Critical error during frame extraction from: $videoPath", e)
        } finally {
            try {
                retriever.release()
                AppLogger.d(AppLogger.TAG_FRAMES, "MediaMetadataRetriever released successfully")
            } catch (e: Exception) {
                AppLogger.e(AppLogger.TAG_FRAMES, "Error releasing MediaMetadataRetriever", e)
            }
        }
        
        frameInfoList
    }
    
    /**
     * Extract a single frame at a specific timestamp
     */
    suspend fun extractFrameAtTime(
        videoPath: String,
        timestampMs: Long,
        targetWidth: Int = DEFAULT_TARGET_SIZE,
        targetHeight: Int = DEFAULT_TARGET_SIZE
    ): Bitmap? = withContext(Dispatchers.IO) {
        
        val retriever = MediaMetadataRetriever()
        
        try {
            retriever.setDataSource(videoPath)
            
            val timestampUs = timestampMs * 1000 // Convert to microseconds
            val bitmap = retriever.getFrameAtTime(timestampUs, MediaMetadataRetriever.OPTION_CLOSEST_SYNC)
            
            return@withContext bitmap?.let { resizeBitmap(it, targetWidth, targetHeight) }
            
        } catch (e: Exception) {
            Log.e(TAG, "Error extracting frame at time $timestampMs", e)
            return@withContext null
        } finally {
            try {
                retriever.release()
            } catch (e: Exception) {
                Log.e(TAG, "Error releasing MediaMetadataRetriever", e)
            }
        }
    }
    
    /**
     * Get video metadata
     */
    suspend fun getVideoMetadata(videoPath: String): VideoMetadata? = withContext(Dispatchers.IO) {
        AppLogger.d(AppLogger.TAG_VIDEO, "Getting video metadata for: $videoPath")
        val retriever = MediaMetadataRetriever()

        try {
            retriever.setDataSource(videoPath)
            AppLogger.d(AppLogger.TAG_VIDEO, "Data source set for metadata extraction")

            val duration = retriever.extractMetadata(MediaMetadataRetriever.METADATA_KEY_DURATION)?.toLongOrNull() ?: 0L
            val width = retriever.extractMetadata(MediaMetadataRetriever.METADATA_KEY_VIDEO_WIDTH)?.toIntOrNull() ?: 0
            val height = retriever.extractMetadata(MediaMetadataRetriever.METADATA_KEY_VIDEO_HEIGHT)?.toIntOrNull() ?: 0
            val frameRate = retriever.extractMetadata(MediaMetadataRetriever.METADATA_KEY_CAPTURE_FRAMERATE)?.toFloatOrNull() ?: 30f

            AppLogger.logVideoMetadata(duration, frameRate, width, height)

            return@withContext VideoMetadata(
                duration = duration,
                width = width,
                height = height,
                frameRate = frameRate
            )

        } catch (e: Exception) {
            AppLogger.e(AppLogger.TAG_VIDEO, "Error getting video metadata from $videoPath", e)
            return@withContext null
        } finally {
            try {
                retriever.release()
                AppLogger.d(AppLogger.TAG_VIDEO, "MediaMetadataRetriever released for metadata")
            } catch (e: Exception) {
                AppLogger.e(AppLogger.TAG_VIDEO, "Error releasing MediaMetadataRetriever for metadata", e)
            }
        }
    }
    
    private fun resizeBitmap(bitmap: Bitmap, targetWidth: Int, targetHeight: Int): Bitmap {
        return if (bitmap.width != targetWidth || bitmap.height != targetHeight) {
            Bitmap.createScaledBitmap(bitmap, targetWidth, targetHeight, true)
        } else {
            bitmap
        }
    }
    
    private fun saveFrameToFile(bitmap: Bitmap, filePath: String): Boolean {
        return try {
            AppLogger.v(AppLogger.TAG_FRAMES, "Saving frame to: $filePath")
            FileOutputStream(filePath).use { out ->
                val compressed = bitmap.compress(Bitmap.CompressFormat.PNG, 90, out)
                AppLogger.v(AppLogger.TAG_FRAMES, "Frame compression result: $compressed")
                compressed
            }
        } catch (e: Exception) {
            AppLogger.e(AppLogger.TAG_FRAMES, "Failed to save frame to $filePath", e)
            false
        }
    }
    
    data class VideoMetadata(
        val duration: Long,
        val width: Int,
        val height: Int,
        val frameRate: Float
    )
}
