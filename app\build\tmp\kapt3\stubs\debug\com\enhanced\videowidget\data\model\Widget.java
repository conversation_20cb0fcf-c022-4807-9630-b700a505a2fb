package com.enhanced.videowidget.data.model;

import java.lang.System;

/**
 * Enhanced Widget data class with pause/play functionality
 */
@kotlin.Metadata(mv = {1, 8, 0}, k = 1, d1 = {"\u00008\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0007\n\u0002\b\u0004\n\u0002\u0010\u000b\n\u0002\b\u0003\n\u0002\u0010\t\n\u0002\b0\b\u0086\b\u0018\u00002\u00020\u0001B\u0091\u0001\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0005\u0012\n\b\u0002\u0010\u0007\u001a\u0004\u0018\u00010\b\u0012\n\b\u0002\u0010\t\u001a\u0004\u0018\u00010\u0005\u0012\b\b\u0002\u0010\n\u001a\u00020\u000b\u0012\b\b\u0002\u0010\f\u001a\u00020\u0005\u0012\b\b\u0002\u0010\r\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u000e\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u000f\u001a\u00020\u0010\u0012\b\b\u0002\u0010\u0011\u001a\u00020\u0010\u0012\n\b\u0002\u0010\u0012\u001a\u0004\u0018\u00010\u0005\u0012\b\b\u0002\u0010\u0013\u001a\u00020\u0014\u0012\b\b\u0002\u0010\u0015\u001a\u00020\u0014\u00a2\u0006\u0002\u0010\u0016J\t\u0010)\u001a\u00020\u0003H\u00c6\u0003J\t\u0010*\u001a\u00020\u0010H\u00c6\u0003J\t\u0010+\u001a\u00020\u0010H\u00c6\u0003J\u000b\u0010,\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003J\t\u0010-\u001a\u00020\u0014H\u00c6\u0003J\t\u0010.\u001a\u00020\u0014H\u00c6\u0003J\t\u0010/\u001a\u00020\u0005H\u00c6\u0003J\t\u00100\u001a\u00020\u0005H\u00c6\u0003J\u000b\u00101\u001a\u0004\u0018\u00010\bH\u00c6\u0003J\u000b\u00102\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003J\t\u00103\u001a\u00020\u000bH\u00c6\u0003J\t\u00104\u001a\u00020\u0005H\u00c6\u0003J\t\u00105\u001a\u00020\u0003H\u00c6\u0003J\t\u00106\u001a\u00020\u0003H\u00c6\u0003J\u009b\u0001\u00107\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u00052\n\b\u0002\u0010\u0007\u001a\u0004\u0018\u00010\b2\n\b\u0002\u0010\t\u001a\u0004\u0018\u00010\u00052\b\b\u0002\u0010\n\u001a\u00020\u000b2\b\b\u0002\u0010\f\u001a\u00020\u00052\b\b\u0002\u0010\r\u001a\u00020\u00032\b\b\u0002\u0010\u000e\u001a\u00020\u00032\b\b\u0002\u0010\u000f\u001a\u00020\u00102\b\b\u0002\u0010\u0011\u001a\u00020\u00102\n\b\u0002\u0010\u0012\u001a\u0004\u0018\u00010\u00052\b\b\u0002\u0010\u0013\u001a\u00020\u00142\b\b\u0002\u0010\u0015\u001a\u00020\u0014H\u00c6\u0001J\u0013\u00108\u001a\u00020\u00102\b\u00109\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\u0006\u0010:\u001a\u00020\u0003J\t\u0010;\u001a\u00020\u0003H\u00d6\u0001J\u0006\u0010<\u001a\u00020\u0010J\t\u0010=\u001a\u00020\u0005H\u00d6\u0001J\u0006\u0010>\u001a\u00020\u0000J\u000e\u0010?\u001a\u00020\u00002\u0006\u0010@\u001a\u00020\u0003J\u0018\u0010A\u001a\u00020\u00002\u0006\u0010B\u001a\u00020\u00102\b\b\u0002\u0010C\u001a\u00020\u0010R\u0011\u0010\u0013\u001a\u00020\u0014\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0017\u0010\u0018R\u0011\u0010\u000e\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0019\u0010\u001aR\u0011\u0010\u0006\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001b\u0010\u001cR\u0013\u0010\u0007\u001a\u0004\u0018\u00010\b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001d\u0010\u001eR\u0011\u0010\n\u001a\u00020\u000b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001f\u0010 R\u0013\u0010\t\u001a\u0004\u0018\u00010\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b!\u0010\u001cR\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\"\u0010\u001aR\u0011\u0010\u0011\u001a\u00020\u0010\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0011\u0010#R\u0011\u0010\u000f\u001a\u00020\u0010\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000f\u0010#R\u0011\u0010\u0015\u001a\u00020\u0014\u00a2\u0006\b\n\u0000\u001a\u0004\b$\u0010\u0018R\u0011\u0010\f\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b%\u0010\u001cR\u0011\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b&\u0010\u001cR\u0011\u0010\r\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\'\u0010\u001aR\u0013\u0010\u0012\u001a\u0004\u0018\u00010\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b(\u0010\u001c\u00a8\u0006D"}, d2 = {"Lcom/enhanced/videowidget/data/model/Widget;", "", "id", "", "name", "", "description", "firstFrame", "Landroid/graphics/Bitmap;", "framesDirectory", "framerate", "", "mediaType", "totalFrames", "currentFrameIndex", "isPlaying", "", "isPaused", "videoPath", "createdAt", "", "lastUpdated", "(ILjava/lang/String;Ljava/lang/String;Landroid/graphics/Bitmap;Ljava/lang/String;FLjava/lang/String;IIZZLjava/lang/String;JJ)V", "getCreatedAt", "()J", "getCurrentFrameIndex", "()I", "getDescription", "()Ljava/lang/String;", "getFirstFrame", "()Landroid/graphics/Bitmap;", "getFramerate", "()F", "getFramesDirectory", "getId", "()Z", "getLastUpdated", "getMediaType", "getName", "getTotalFrames", "getVideoPath", "component1", "component10", "component11", "component12", "component13", "component14", "component2", "component3", "component4", "component5", "component6", "component7", "component8", "component9", "copy", "equals", "other", "getNextFrameIndex", "hashCode", "shouldAnimate", "toString", "togglePlayPause", "withFrameIndex", "frameIndex", "withPlaybackState", "playing", "paused", "app_debug"})
public final class Widget {
    private final int id = 0;
    @org.jetbrains.annotations.NotNull
    private final java.lang.String name = null;
    @org.jetbrains.annotations.NotNull
    private final java.lang.String description = null;
    @org.jetbrains.annotations.Nullable
    private final android.graphics.Bitmap firstFrame = null;
    @org.jetbrains.annotations.Nullable
    private final java.lang.String framesDirectory = null;
    private final float framerate = 0.0F;
    @org.jetbrains.annotations.NotNull
    private final java.lang.String mediaType = null;
    private final int totalFrames = 0;
    private final int currentFrameIndex = 0;
    private final boolean isPlaying = false;
    private final boolean isPaused = false;
    @org.jetbrains.annotations.Nullable
    private final java.lang.String videoPath = null;
    private final long createdAt = 0L;
    private final long lastUpdated = 0L;
    
    /**
     * Enhanced Widget data class with pause/play functionality
     */
    @org.jetbrains.annotations.NotNull
    public final com.enhanced.videowidget.data.model.Widget copy(int id, @org.jetbrains.annotations.NotNull
    java.lang.String name, @org.jetbrains.annotations.NotNull
    java.lang.String description, @org.jetbrains.annotations.Nullable
    android.graphics.Bitmap firstFrame, @org.jetbrains.annotations.Nullable
    java.lang.String framesDirectory, float framerate, @org.jetbrains.annotations.NotNull
    java.lang.String mediaType, int totalFrames, int currentFrameIndex, boolean isPlaying, boolean isPaused, @org.jetbrains.annotations.Nullable
    java.lang.String videoPath, long createdAt, long lastUpdated) {
        return null;
    }
    
    /**
     * Enhanced Widget data class with pause/play functionality
     */
    @java.lang.Override
    public boolean equals(@org.jetbrains.annotations.Nullable
    java.lang.Object other) {
        return false;
    }
    
    /**
     * Enhanced Widget data class with pause/play functionality
     */
    @java.lang.Override
    public int hashCode() {
        return 0;
    }
    
    /**
     * Enhanced Widget data class with pause/play functionality
     */
    @org.jetbrains.annotations.NotNull
    @java.lang.Override
    public java.lang.String toString() {
        return null;
    }
    
    public Widget(int id, @org.jetbrains.annotations.NotNull
    java.lang.String name, @org.jetbrains.annotations.NotNull
    java.lang.String description, @org.jetbrains.annotations.Nullable
    android.graphics.Bitmap firstFrame, @org.jetbrains.annotations.Nullable
    java.lang.String framesDirectory, float framerate, @org.jetbrains.annotations.NotNull
    java.lang.String mediaType, int totalFrames, int currentFrameIndex, boolean isPlaying, boolean isPaused, @org.jetbrains.annotations.Nullable
    java.lang.String videoPath, long createdAt, long lastUpdated) {
        super();
    }
    
    public final int component1() {
        return 0;
    }
    
    public final int getId() {
        return 0;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String component2() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String getName() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String component3() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String getDescription() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final android.graphics.Bitmap component4() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final android.graphics.Bitmap getFirstFrame() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String component5() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String getFramesDirectory() {
        return null;
    }
    
    public final float component6() {
        return 0.0F;
    }
    
    public final float getFramerate() {
        return 0.0F;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String component7() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String getMediaType() {
        return null;
    }
    
    public final int component8() {
        return 0;
    }
    
    public final int getTotalFrames() {
        return 0;
    }
    
    public final int component9() {
        return 0;
    }
    
    public final int getCurrentFrameIndex() {
        return 0;
    }
    
    public final boolean component10() {
        return false;
    }
    
    public final boolean isPlaying() {
        return false;
    }
    
    public final boolean component11() {
        return false;
    }
    
    public final boolean isPaused() {
        return false;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String component12() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String getVideoPath() {
        return null;
    }
    
    public final long component13() {
        return 0L;
    }
    
    public final long getCreatedAt() {
        return 0L;
    }
    
    public final long component14() {
        return 0L;
    }
    
    public final long getLastUpdated() {
        return 0L;
    }
    
    /**
     * Get the next frame index for animation
     */
    public final int getNextFrameIndex() {
        return 0;
    }
    
    /**
     * Check if widget should be animating
     */
    public final boolean shouldAnimate() {
        return false;
    }
    
    /**
     * Create a copy with updated playback state
     */
    @org.jetbrains.annotations.NotNull
    public final com.enhanced.videowidget.data.model.Widget withPlaybackState(boolean playing, boolean paused) {
        return null;
    }
    
    /**
     * Create a copy with updated frame index
     */
    @org.jetbrains.annotations.NotNull
    public final com.enhanced.videowidget.data.model.Widget withFrameIndex(int frameIndex) {
        return null;
    }
    
    /**
     * Toggle pause/play state
     */
    @org.jetbrains.annotations.NotNull
    public final com.enhanced.videowidget.data.model.Widget togglePlayPause() {
        return null;
    }
}