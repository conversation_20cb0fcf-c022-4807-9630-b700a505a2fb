<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/status_bar_latest_event_content"
    android:layout_width="match_parent"
    android:layout_height="128dp">
    <include
        android:layout_width="@dimen/notification_large_icon_width"
        android:layout_height="@dimen/notification_large_icon_height"
        layout="@layout/notification_template_icon_group"/>
    <include
        android:layout_width="48dp"
        android:layout_height="48dp"
        android:layout_marginLeft="2dp"
        android:layout_marginRight="2dp"
        android:layout_alignParentRight="true"
        android:layout_alignParentEnd="true"
        layout="@layout/notification_media_cancel_action"/>
    <LinearLayout
        android:orientation="horizontal"
        android:id="@+id/notification_main_column_container"
        android:paddingTop="@dimen/notification_main_column_padding_top"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/notification_large_icon_height"
        android:minHeight="@dimen/notification_large_icon_height"
        android:layout_toLeftOf="@+id/cancel_action"
        android:layout_marginStart="@dimen/notification_large_icon_height"
        android:layout_toStartOf="@+id/cancel_action">
        <FrameLayout
            android:id="@+id/notification_main_column"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="@dimen/notification_content_margin_start"
            android:layout_marginRight="8dp"
            android:layout_marginBottom="8dp"
            android:layout_weight="1"
            android:layout_marginStart="@dimen/notification_content_margin_start"
            android:layout_marginEnd="8dp"/>
        <FrameLayout
            android:id="@+id/right_side"
            android:paddingTop="@dimen/notification_right_side_padding_top"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginRight="8dp"
            android:layout_marginEnd="8dp">
            <DateTimeView
                android:textAppearance="@style/TextAppearance.Compat.Notification.Time.Media"
                android:layout_gravity="end|top"
                android:id="@+id/time"
                android:visibility="gone"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:singleLine="true"/>
            <Chronometer
                android:textAppearance="@style/TextAppearance.Compat.Notification.Time.Media"
                android:layout_gravity="end|top"
                android:id="@+id/chronometer"
                android:visibility="gone"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:singleLine="true"/>
            <TextView
                android:textAppearance="@style/TextAppearance.Compat.Notification.Info.Media"
                android:layout_gravity="end|bottom"
                android:id="@+id/info"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="20dp"
                android:singleLine="true"/>
        </FrameLayout>
    </LinearLayout>
    <LinearLayout
        android:orientation="horizontal"
        android:id="@+id/media_actions"
        android:layout_width="match_parent"
        android:layout_height="48dp"
        android:layout_marginLeft="12dp"
        android:layout_marginRight="12dp"
        android:layout_alignParentBottom="true"
        android:layoutDirection="ltr"/>
    <ImageView
        android:id="@+id/action_divider"
        android:background="?android:attr/dividerHorizontal"
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_above="@+id/media_actions"/>
</RelativeLayout>
