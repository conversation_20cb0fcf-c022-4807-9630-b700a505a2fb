{"logs": [{"outputFile": "com.enhanced.videowidget.app-mergeDebugResources-45:/values-sw/values-sw.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\3c82f0ccdbe9412677623492bdd8999d\\transformed\\appcompat-1.6.1\\res\\values-sw\\values-sw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,208,307,415,505,610,727,810,892,983,1076,1171,1265,1365,1458,1553,1647,1738,1829,1911,2012,2120,2219,2326,2438,2542,2704,2801", "endColumns": "102,98,107,89,104,116,82,81,90,92,94,93,99,92,94,93,90,90,81,100,107,98,106,111,103,161,96,82", "endOffsets": "203,302,410,500,605,722,805,887,978,1071,1166,1260,1360,1453,1548,1642,1733,1824,1906,2007,2115,2214,2321,2433,2537,2699,2796,2879"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,102", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "309,412,511,619,709,814,931,1014,1096,1187,1280,1375,1469,1569,1662,1757,1851,1942,2033,2115,2216,2324,2423,2530,2642,2746,2908,8739", "endColumns": "102,98,107,89,104,116,82,81,90,92,94,93,99,92,94,93,90,90,81,100,107,98,106,111,103,161,96,82", "endOffsets": "407,506,614,704,809,926,1009,1091,1182,1275,1370,1464,1564,1657,1752,1846,1937,2028,2110,2211,2319,2418,2525,2637,2741,2903,3000,8817"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8c486cec99d4fccb7893779ad5d22534\\transformed\\material-1.9.0\\res\\values-sw\\values-sw.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,259,335,409,482,579,668,767,896,979,1047,1139,1212,1275,1361,1423,1486,1551,1619,1682,1736,1868,1925,1987,2041,2115,2253,2334,2414,2546,2631,2718,2806,2860,2913,2979,3051,3133,3223,3295,3370,3441,3514,3611,3685,3780,3877,3951,4036,4136,4189,4257,4345,4435,4497,4561,4624,4741,4851,4962,5074,5132,5189", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "endColumns": "12,75,73,72,96,88,98,128,82,67,91,72,62,85,61,62,64,67,62,53,131,56,61,53,73,137,80,79,131,84,86,87,53,52,65,71,81,89,71,74,70,72,96,73,94,96,73,84,99,52,67,87,89,61,63,62,116,109,110,111,57,56,80", "endOffsets": "254,330,404,477,574,663,762,891,974,1042,1134,1207,1270,1356,1418,1481,1546,1614,1677,1731,1863,1920,1982,2036,2110,2248,2329,2409,2541,2626,2713,2801,2855,2908,2974,3046,3128,3218,3290,3365,3436,3509,3606,3680,3775,3872,3946,4031,4131,4184,4252,4340,4430,4492,4556,4619,4736,4846,4957,5069,5127,5184,5265"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3005,3081,3155,3228,3325,4137,4236,4365,4448,4516,4608,4681,4744,4830,4892,4955,5020,5088,5151,5205,5337,5394,5456,5510,5584,5722,5803,5883,6015,6100,6187,6275,6329,6382,6448,6520,6602,6692,6764,6839,6910,6983,7080,7154,7249,7346,7420,7505,7605,7658,7726,7814,7904,7966,8030,8093,8210,8320,8431,8543,8601,8658", "endLines": "5,33,34,35,36,37,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101", "endColumns": "12,75,73,72,96,88,98,128,82,67,91,72,62,85,61,62,64,67,62,53,131,56,61,53,73,137,80,79,131,84,86,87,53,52,65,71,81,89,71,74,70,72,96,73,94,96,73,84,99,52,67,87,89,61,63,62,116,109,110,111,57,56,80", "endOffsets": "304,3076,3150,3223,3320,3409,4231,4360,4443,4511,4603,4676,4739,4825,4887,4950,5015,5083,5146,5200,5332,5389,5451,5505,5579,5717,5798,5878,6010,6095,6182,6270,6324,6377,6443,6515,6597,6687,6759,6834,6905,6978,7075,7149,7244,7341,7415,7500,7600,7653,7721,7809,7899,7961,8025,8088,8205,8315,8426,8538,8596,8653,8734"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\6fe3e64a80094b4316c7006f0bcdcf47\\transformed\\core-1.10.1\\res\\values-sw\\values-sw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,149,251,348,449,556,663,778", "endColumns": "93,101,96,100,106,106,114,100", "endOffsets": "144,246,343,444,551,658,773,874"}, "to": {"startLines": "38,39,40,41,42,43,44,103", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3414,3508,3610,3707,3808,3915,4022,8822", "endColumns": "93,101,96,100,106,106,114,100", "endOffsets": "3503,3605,3702,3803,3910,4017,4132,8918"}}]}]}