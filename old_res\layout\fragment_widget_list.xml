<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@color/background"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:backgroundTint="@color/background">
    <TextView
        android:textSize="24sp"
        android:textStyle="bold"
        android:textColor="@color/textColor"
        android:id="@+id/headerText"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="44dp"
        android:text="@string/widget_list"
        android:layout_marginStart="16dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>
    <com.google.android.material.button.MaterialButton
        android:id="@+id/removeAdsBtn"
        android:background="?attr/selectableItemBackgroundBorderless"
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:contentDescription="@string/remove_ads"
        android:layout_marginEnd="10dp"
        app:icon="@drawable/ic_remove_ads"
        app:iconGravity="textStart"
        app:iconPadding="-4dp"
        app:iconSize="30dp"
        app:iconTint="@color/textColor"
        app:layout_constraintBottom_toBottomOf="@+id/headerText"
        app:layout_constraintEnd_toStartOf="@+id/notificationBtn"
        app:layout_constraintTop_toTopOf="@+id/headerText"
        style="@style/Widget.MaterialComponents.Button.Icon"/>
    <com.google.android.material.button.MaterialButton
        android:id="@+id/notificationBtn"
        android:background="?attr/selectableItemBackgroundBorderless"
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:contentDescription=""
        android:layout_marginEnd="10dp"
        app:icon="@drawable/ic_notification_blue_dot"
        app:iconGravity="textStart"
        app:iconPadding="-4dp"
        app:iconSize="26dp"
        app:iconTint="@null"
        app:layout_constraintBottom_toBottomOf="@+id/headerText"
        app:layout_constraintEnd_toStartOf="@+id/moreBtn"
        app:layout_constraintTop_toTopOf="@+id/headerText"
        style="@style/Widget.MaterialComponents.Button.Icon"/>
    <com.google.android.material.button.MaterialButton
        android:id="@+id/moreBtn"
        android:background="?attr/selectableItemBackgroundBorderless"
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:contentDescription="@string/more"
        android:layout_marginEnd="15dp"
        app:icon="@drawable/ic_more"
        app:iconGravity="textStart"
        app:iconPadding="-4dp"
        app:iconSize="26dp"
        app:iconTint="#bcbcbc"
        app:layout_constraintBottom_toBottomOf="@+id/headerText"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/headerText"
        style="@style/Widget.MaterialComponents.Button.Icon"/>
    <FrameLayout
        android:id="@+id/tabFrame"
        android:paddingLeft="32dp"
        android:paddingTop="32dp"
        android:paddingRight="32dp"
        android:paddingBottom="16dp"
        android:clipToPadding="false"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:elevation="-2dp"
        android:paddingHorizontal="32dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/headerText">
        <View
            android:background="@drawable/tab_bg"
            android:layout_width="match_parent"
            android:layout_height="50dp"/>
        <View
            android:id="@+id/indicator"
            android:background="@drawable/tab_bg"
            android:layout_width="match_parent"
            android:layout_height="50dp"/>
        <com.google.android.material.tabs.TabLayout
            android:id="@+id/tab"
            android:background="#00000000"
            android:layout_width="match_parent"
            android:layout_height="50dp"
            app:tabGravity="fill"
            app:tabIndicatorColor="#017eff"
            app:tabMode="fixed"
            app:tabRippleColor="@null"
            app:tabSelectedTextColor="@color/textColor"
            app:tabTextColor="@color/textColorSecondary"/>
    </FrameLayout>
    <androidx.viewpager.widget.ViewPager
        android:id="@+id/viewPager"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:elevation="-2dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tabFrame"/>
</androidx.constraintlayout.widget.ConstraintLayout>
